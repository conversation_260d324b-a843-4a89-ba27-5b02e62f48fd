<?php 
include_once '../Lib/DbConnect.php';

	class client_indv_identification {
		
		
		private $tableName = 'client_indv_identification';
		private $dbConn;
		
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $ClientId;
		private $Type;
		private $IdNumber;

		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setClientId($ClientId) { $this->ClientId = $ClientId; }
		function getClientId() { return $this->ClientId; }
		
		function setType($Type) { $this->Type = $Type; }
		function getType() { return $this->Type; }
		
		function setIdNumber($IdNumber) { $this->IdNumber = $IdNumber; }
		function getIdNumber() { return $this->IdNumber; }
		
	
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
				
		public function client_indv_identification() {
			
			$sql = 'INSERT INTO  client_indv_identification (Creator,Created,ClientId,Type,IdNumber) 
												     VALUES (:Creator,:Created,:ClientId,:Type,:IdNumber)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':ClientId', $this->ClientId);
			$stmt->bindParam(':Type', $this->Type);
			$stmt->bindParam(':IdNumber', $this->IdNumber);
						
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>