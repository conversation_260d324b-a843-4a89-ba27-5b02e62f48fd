<?php 
include_once '../Lib/DbConnect.php';

	class UserModel {
		
		private $tableName = 'tbluser';
		private $dbConn;
		
		private $UserID;
		private $LName;
		private $FName;
		private $MName;
		private $EmployeeName;
		private $Position;
		private $UserName;
		private $Password;
		private $DateCreated;
		private $Flag;
		private $LogIn;
		private $ExpiryDate;
		private $hash_uname;
		
		

		function setUserID($UserID) { $this->UserID = $UserID; }
		function getUserID() { return $this->UserID; }
		
		function setFName($FName) { $this->FName = $FName; }
		function getFName() { return $this->FName; }
		
		function setMName($MName) { $this->MName = $MName; }
		function getMName() { return $this->MName; }
		
		function setLName($LName) { $this->LName = $LName; }
		function getLName() { return $this->LName; }
		
		function setEmployeeName($EmployeeName) { $this->EmployeeName = $EmployeeName; }
		function getEmployeeName() { return $this->EmployeeName; }
		
		function setPosition($Position) { $this->Position = $Position; }
		function getPosition() { return $this->Position; }
		
		function setUserName($UserName) { $this->UserName = $UserName; }
		function getUserName() { return $this->UserName; }
		
		function setPassword($Password) { $this->Password = $Password; }
		function getPassword() { return $this->Password; }
		
		function setDateCreated($DateCreated) { $this->DateCreated = $DateCreated; }
		function getDateCreated() { return $this->DateCreated; }
		
		function setFlag($Flag) { $this->Flag = $Flag; }
		function getFlag() { return $this->Flag; }
		
		function setLogin($Login) { $this->Login = $Login; }
		function getLogin() { return $this->Login; }
		
		function setExpiryDate($ExpiryDate) { $this->ExpiryDate = $ExpiryDate; }
		function getExpiryDate() { return $this->ExpiryDate; }
		
		function sethash_uname($hash_uname) { $this->hash_uname = $hash_uname; }
		function gethash_uname() { return $this->hash_uname; }
		

		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		public function getUserDetailsById() {
					
					
					$sql = "select * from tbluser a where a.UserID = :UserID";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':UserID', $this->UserID);
					$stmt->execute();
					$user = $stmt->fetch(PDO::FETCH_ASSOC);
					
					return $user;
					
		}
		
		public function getAllUser() {
					
					$sql = "select * from tbluser";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->execute();
					$user = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $user;
		}
				
		public function insert() {
			
			$sql = 'INSERT INTO  tbluser (FName,MName,LName,EmployeeName,Position,UserName,Password,DateCreated,Flag,Login,ExpiryDate,hash_uname) 
														VALUES(:FName,:MName,:LName,:EmployeeName,:Position,:UserName,:Password,:DateCreated,:Flag,:Login,:ExpiryDate,:hash_uname)';
			
			$stmt = $this->dbConn->prepare($sql);
			
			$stmt->bindParam(':FName', $this->FName);
			$stmt->bindParam(':MName', $this->MName);
			$stmt->bindParam(':LName', $this->LName);
			$stmt->bindParam(':EmployeeName', $this->EmployeeName);
			$stmt->bindParam(':Position', $this->Position);
			$stmt->bindParam(':UserName', $this->UserName);
			$stmt->bindParam(':Password', $this->Password);
			$stmt->bindParam(':DateCreated', $this->DateCreated);
			$stmt->bindParam(':Flag', $this->Flag);
			$stmt->bindParam(':Login', $this->Login);
			$stmt->bindParam(':ExpiryDate', $this->ExpiryDate);
			$stmt->bindParam(':hash_uname', $this->hash_uname);	
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>