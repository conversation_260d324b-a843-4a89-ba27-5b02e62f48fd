<?php 
include_once '../Lib/DbConnect.php';

	class ColorPricingModel {
		
		public $dbConn;
		
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		// public function getColorPricingByDealerBranchKey() {
					
					
					// $sql = "select * from DealerPricing a where a.DealerBranch<PERSON><PERSON> = :DealerBranchKey";
					// $stmt = $this->dbConn->prepare($sql);
					// $stmt->bindParam(':DealerBranchKey', $this->DealerBranchKey);
					// $stmt->execute();
					// $DealerPricing = $stmt->fetchAll(PDO::FETCH_ASSOC);
					// return $DealerPricing;
					
		// }
		
		public function getAllColorPricing() {
					
					$sql = "SELECT distinct A.SysId,B.Description, a.AcuInvId
							FROM tblinvcolor A
							LEFT JOIN tblcolor B ON A.ColorId=B.ColorId
							LEFT JOIN DealerPricing c on a.AcuInvId = c.acu_InvId
							WHERE a.AcuInvId IS NOT NULL and a.Status = 1";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->execute();
					$ColorPricing = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $ColorPricing;
		}
	}
 ?>