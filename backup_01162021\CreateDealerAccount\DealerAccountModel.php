<?php 
include_once '../Lib/DbConnect.php';

	class DealerAccountModel {
		
		public $tableName = 'DealerAccount';
		public $dbConn;

		public $SysId;
		public $Created;
		public $Creator;
		public $LastModified;
		public $Modifier;
		public $Description;
		public $Title;
		public $Email;
		public $ContactPerson;
		public $ContactNumber;
		public $IsAcceptBrandNew;
		public $IsAcceptRepo;
		public $Rebate_Max;
		public $Rebate_Min;
		public $Rebate_Exception;
		public $CurrentPassword;
		public $LastChangedPassword;
		public $Status;
		public $InterestRate;
		public $DPRate2;
		public $DPRate1;
		public $IsUseDPRate;
		public $MinSRPForDPRate2;
		public $InternalAccount;
		public $IsManualInputPrice;
	
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setDescription($Description) { $this->Description = $Description; }
		function getDescription() { return $this->Description; }
		
		function setTitle($Title) { $this->Title = $Title; }
		function getTitle() { return $this->Title; }
		
		function setEmail($Email) { $this->Email = $Email; }
		function getEmail() { return $this->Email; }
				
		function setContactPerson($ContactPerson) { $this->ContactPerson = $ContactPerson; }
		function getContactPerson() { return $this->ContactPerson; }
	
		function setContactNumber($ContactNumber) { $this->ContactNumber = $ContactNumber; }
		function getContactNumber() { return $this->ContactNumber; }
	
		function setIsAcceptBrandNew($IsAcceptBrandNew) { $this->IsAcceptBrandNew = $IsAcceptBrandNew; }
		function getIsAcceptBrandNew() { return $this->IsAcceptBrandNew; }
	
		function setIsAcceptRepo($IsAcceptRepo) { $this->IsAcceptRepo = $IsAcceptRepo; }
		function getIsAcceptRepo() { return $this->IsAcceptRepo; }
	
		function setRebate_Max($Rebate_Max) { $this->Rebate_Max = $Rebate_Max; }
		function getRebate_Max() { return $this->Rebate_Max; }
	
		function setRebate_Min($Rebate_Min) { $this->Rebate_Min = $Rebate_Min; }
		function getRebate_Min() { return $this->Rebate_Min; }
	
		function setRebate_Exception($Rebate_Exception) { $this->Rebate_Exception = $Rebate_Exception; }
		function getRebate_Exception() { return $this->Rebate_Exception; }
	
		function setCurrentPassword($CurrentPassword) { $this->CurrentPassword = $CurrentPassword; }
		function getCurrentPassword() { return $this->CurrentPassword; }
	
		function setLastChangedPassword($LastChangedPassword) { $this->LastChangedPassword = $LastChangedPassword; }
		function getLastChangedPassword() { return $this->LastChangedPassword; }
	
		function setStatus($Status) { $this->Status = $Status; }
		function getStatus() { return $this->Status; }
	
		function setInterestRate($InterestRate) { $this->InterestRate = $InterestRate; }
		function getInterestRate() { return $this->InterestRate; }
	
		function setDPRate2($DPRate2) { $this->DPRate2 = $DPRate2; }
		function getDPRate2() { return $this->DPRate2; }
	
		function setDPRate1($DPRate1) { $this->DPRate1 = $DPRate1; }
		function getDPRate1() { return $this->DPRate1; }
	
		function setIsUseDPRate($IsUseDPRate) { $this->IsUseDPRate = $IsUseDPRate; }
		function getIsUseDPRate() { return $this->IsUseDPRate; }
	
		function setMinSRPForDPRate2($MinSRPForDPRate2) { $this->MinSRPForDPRate2 = $MinSRPForDPRate2; }
		function getMinSRPForDPRate2() { return $this->MinSRPForDPRate2; }
	
		function setInternalAccount($InternalAccount) { $this->InternalAccount = $InternalAccount; }
		function getInternalAccount() { return $this->InternalAccount; }
	
		function setIsManualInputPrice($IsManualInputPrice) { $this->IsManualInputPrice = $IsManualInputPrice; }
		function getIsManualInputPrice() { return $this->IsManualInputPrice; }
	
		
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		public function getDealerAccountByTitle() {
					
					
					$sql = "select * from DealerAccount a where a.Title = :Title";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':Title', $this->Title);
					$stmt->execute();
					$DealerAccount = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $DealerAccount;
					
		}
		
		public function getAllDealerAccount() {
					
					$sql = "select  SysId,
								Created,
								Creator,
								LastModified,
								Modifier,
								Title,
								Description,
								Email,
								ContactPerson,
								ContactNumber,
								IsAcceptBrandNew,
								IsAcceptRepo,
								Rebate_Max,
								Rebate_Min,
								Rebate_Exception,
								CurrentPassword,
								LastChangedPassword,
								Status,
								concat(InterestRate,'') InterestRate,
								concat(DPRate1,'') DPRate1,
								concat(DPRate2,'') DPRate2,
								IsUseDPRate,
								MinSRPForDPRate2,
								InternalAccount,
								IsManualInputPrice

					from DealerAccount";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->execute();
					$DealerAccount = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $DealerAccount;
					
					// $r = array();
					// $rs = sqlsrv_query($conn,$str);
						
					// while($row = sqlsrv_fetch_array($rs)){	
					
					 // array_push($r,$row);
							
					// }
					// sqlsrv_free_stmt($rs);
					// echo json_encode($r);
		}
	}
 ?>