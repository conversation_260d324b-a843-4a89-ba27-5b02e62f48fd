<?php 
include_once '../Lib/DbConnect.php';

	class LookupValuesModel {
		
		public $tableName = 'master_lookup_values';
		public $dbConn;
		
		public $SysId;
		public $Created;
		public $Creator;
		public $LastModified;
		public $Modifier;
		public $Display;
		public $Value;
		public $Note;
		public $OrderNumber;
		public $Category;
		public $IsActive;
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setDisplay($Display) { $this->Display = $Display; }
		function getDisplay() { return $this->Display; }
		
		function setValue($Value) { $this->Value = $Value; }
		function getValue() { return $this->Value; }
		
		function setNote($Note) { $this->Note = $Note; }
		function getNote() { return $this->Note; }
		
		function setOrderNumber($OrderNumber) { $this->OrderNumber = $OrderNumber; }
		function getOrderNumber() { return $this->OrderNumber; }
		
		function setCategory($Category) { $this->Category = $Category; }
		function getCategory() { return $this->Category; }
		
		function setIsActive($IsActive) { $this->IsActive = $IsActive; }
		function getIsActive() { return $this->IsActive; }
		
		function setExpiryDate($ExpiryDate) { $this->ExpiryDate = $ExpiryDate; }
		function getExpiryDate() { return $this->ExpiryDate; }
		
		function sethash_uname($hash_uname) { $this->hash_uname = $hash_uname; }
		function gethash_uname() { return $this->hash_uname; }
		

		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		public function getLookupValuesByCategory() {
					
					
					$sql = "select * from master_lookup_values a where a.Category = :Category";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':Category', $this->Category);
					$stmt->execute();
					$lookupvalues = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $lookupvalues;
					
		}
		
		public function getAllLookupValues() {
					
					$sql = "select * from master_lookup_values";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->execute();
					$lookupvalues = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $lookupvalues;
		}
		
		public function updateLV() {
			
			$sql = 'update master_lookup_values set LastModified=:LastModified,Modifier=:Modifier,Display=:Display,
													Value=:Value,Note=:Note,OrderNumber=:OrderNumber,Category=:Category,
													IsActive=:IsActive where SysId =:SysId';
			
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':LastModified', $this->LastModified);
			$stmt->bindParam(':Modifier', $this->Modifier);
			$stmt->bindParam(':Display', $this->Display);
			$stmt->bindParam(':Value', $this->Value);
			$stmt->bindParam(':Note', $this->Note);
			$stmt->bindParam(':OrderNumber', $this->OrderNumber);
			$stmt->bindParam(':Category', $this->Category);
			$stmt->bindParam(':IsActive', $this->IsActive);
			$stmt->bindParam(':SysId', $this->SysId);
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
				
		public function insertLV() {
			

			$sql = 'INSERT INTO  master_lookup_values (Creator,Created,Display,Value,Note,OrderNumber,Category,IsActive) 
												VALUES(:Creator,:Created,:Display,:Value,:Note,:OrderNumber,:Category,:IsActive)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':Display', $this->Display);
			$stmt->bindParam(':Value', $this->Value);
			$stmt->bindParam(':Note', $this->Note);
			$stmt->bindParam(':OrderNumber', $this->OrderNumber);
			$stmt->bindParam(':Category', $this->Category);
			$stmt->bindParam(':IsActive', $this->IsActive);
			
			if($stmt->execute()) {				 
			
				 // $SysId = $this->dbConn->lastInsertId();
				 // return $SysId;
				 
				return true;
				
			} else {
				return false;
			}
		}
	}
 ?>