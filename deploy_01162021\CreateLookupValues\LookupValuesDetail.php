<?php 
	include_once '../Lib/jwt.php';
	include_once 'restLookupValuesDetail.php';
	
	class LookupValuesDetail extends RestLookupValues {
		
		public function __construct() {
			parent::__construct();
		}

		public function addLookupValues() {
			
			
			$data = json_decode($this->request, true);
			
			foreach($data as $obj){
				
			$Display = $this->validateParameter('Display', $obj['Display'], STRING, false);
			$Value = $this->validateParameter('Value', $obj['Value'], STRING, false);
			$Note = $this->validateParameter('Note', $obj['Note'], STRING, false);
			$OrderNumber = $this->validateParameter('OrderNumber', $obj['OrderNumber'], INTEGER, false);
			$Category = $this->validateParameter('Category', $obj['Category'], STRING, false);
			$IsActive  = $this->validateParameter('IsActive', $obj['IsActive'], INTEGER, false);
			
			$lvm = new LookupValuesModel;
			$lvm->setDisplay($Display);
			$lvm->setValue($Value);
			$lvm->setNote($Note);
			$lvm->setOrderNumber($OrderNumber);
			$lvm->setCategory($Category);
			$lvm->setIsActive($IsActive);
			$lvm->setCreated(date('Y-m-d'));
			$lvm->setCreator($this->userId);
			
			
			if(!$lvm->insertLV()) {
				
				 $message = 'Failed to insert.';
			} else {
							
				$message = 'Inserted successful.';
				  
				// $message = $lvm->insertLV();
			}
			
		  }
			  $this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function updateLookupValues() {
			
			
			$data = json_decode($this->request, true);
			
			foreach($data as $obj){
				
			$Display = $this->validateParameter('Display', $obj['Display'], STRING, false);
			$Value = $this->validateParameter('Value', $obj['Value'], STRING, false);
			$Note = $this->validateParameter('Note', $obj['Note'], STRING, false);
			$OrderNumber = $this->validateParameter('OrderNumber', $obj['OrderNumber'], INTEGER, false);
			$Category = $this->validateParameter('Category', $obj['Category'], STRING, false);
			$IsActive  = $this->validateParameter('IsActive', $obj['IsActive'], INTEGER, false);
			
			$lvm = new LookupValuesModel;
			$lvm->setSysId($_GET['SysId']);
			$lvm->setDisplay($Display);
			$lvm->setValue($Value);
			$lvm->setNote($Note);
			$lvm->setOrderNumber($OrderNumber);
			$lvm->setCategory($Category);
			$lvm->setIsActive($IsActive);
			$lvm->setLastModified(date('Y-m-d'));
			$lvm->setModifier($this->userId);
			
			
			if(!$lvm->updateLV()) {
				$message = 'Failed to update.';
			} else {
				$message = "Update successfully.";
			}
			
		  }
			$this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function getLookupValuesDetails() {
			
			if(!isset($_GET['Category']) || $_GET['Category'] == "") {
				
				$lookupvalues = new LookupValuesModel;
				$lookupvalues = $lookupvalues->getAllLookupValues();
				// $this->returnResponseGetAll($lookupvalues);	
				$this->returnResponse(SUCCESS_RESPONSE, $lookupvalues);				
			
				
			}else{
			
				$Category = $this->validateParameter('Category', $_GET['Category'], STRING);
			
				$lookupvalues = new LookupValuesModel;
				$lookupvalues->setCategory($Category);
				
				$lookupvalues = $lookupvalues->getLookupValuesByCategory();
				if(!is_array($lookupvalues)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'Lookup values details not found.']);
				}
											
				$this->returnResponseGetAll($lookupvalues);
				// $this->returnResponse(SUCCESS_RESPONSE, $lookupvalues);
			}
			
		}
		
		public function getLookupValuesDetailsByCategory() {
			
			if(!isset($_GET['UserID']) || $_GET['UserID'] == "") {
				
				$user = new UserModel;
				$user = $user->getAlluser();
	//			$this->returnResponseGetAll($user);		
				$this->returnResponse(SUCCESS_RESPONSE, $user);				
				
			}else{
			
				$UserID = $this->validateParameter('UserID', $_GET['UserID'], INTEGER);
			
				$user = new UserModel;
				$user->setUserID($UserID);
				
				$user = $user->getuserDetailsById();
				if(!is_array($user)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $user);
			}
			
		}
	}
	
 ?>