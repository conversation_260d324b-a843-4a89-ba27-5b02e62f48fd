<?php 
	include_once '../Lib/jwt.php';
	include_once 'restMasterProvinceDetail.php';
	
	class MasterProvinceDetail extends RestMasterProvince {
		
		public function __construct() {
			parent::__construct();
		}

		public function addMasterProvince() {
			
			
			$data = json_decode($this->request, true);
			
			foreach($data as $obj){
				
			$MappingId = $this->validateParameter('MappingId', $obj['MappingId'], STRING, false);
			$Title = $this->validateParameter('Title', $obj['Title'], STRING, false);
			$ParentId = $this->validateParameter('ParentId', $obj['ParentId'], STRING, false);
			$OrderNumber = $this->validateParameter('OrderNumber', $obj['OrderNumber'], INTEGER, false);
			
			
			$mrm = new MasterProvinceModel;
			$mrm->setMappingId($MappingId);
			$mrm->setTitle($Title);
			$mrm->setParentId($ParentId);
			$mrm->setOrderNumber($OrderNumber);
			$mrm->setCreated(date('Y-m-d'));
			$mrm->setCreator($this->userId);
			
			
			if(!$mrm->insertPM()) {
				
				 $message = 'Failed to insert.';
			} else {
							
				$message = 'Inserted successful.';
				  
				// $message = $mrm->insertLV();
			}
			
		  }
			  $this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function updateMasterProvince() {
			
			
			$data = json_decode($this->request, true);
			
			foreach($data as $obj){
				
			$MappingId = $this->validateParameter('MappingId', $obj['MappingId'], STRING, false);
			$Title = $this->validateParameter('Title', $obj['Title'], STRING, false);
			$ParentId = $this->validateParameter('ParentId', $obj['ParentId'], STRING, false);
			$OrderNumber = $this->validateParameter('OrderNumber', $obj['OrderNumber'], INTEGER, false);
			
			
			$mrm = new LookupValuesModel;
			$mrm->setSysId($_GET['SysId']);
			$mrm->setMappingId($MappingId);
			$mrm->setTitle($Title);
			$mrm->setParentId($ParentId);
			$mrm->setOrderNumber($OrderNumber);
			$mrm->setLastModified(date('Y-m-d'));
			$mrm->setModifier($this->userId);
			
			
			if(!$mrm->updatePM()) {
				$message = 'Failed to update.';
			} else {
				$message = "Update successfully.";
			}
			
		  }
			$this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function getMasterProvinceDetails() {
			
			if(!isset($_GET['Title']) || $_GET['Title'] == "") {
				
				$masterprovince = new MasterProvinceModel;
				$masterprovince = $masterprovince->getAllMasterProvince();
				$this->returnResponse(SUCCESS_RESPONSE, $masterprovince);
				// $this->returnResponseGetAll($masterprovince);				
				
			}else{
			
				$Title = $this->validateParameter('Title', $_GET['Title'], STRING);
			
				$masterprovince = new MasterProvinceModel;
				$masterprovince->setUserID($Title);
				
				$masterprovince = $masterprovince->getMasterProvinceByTitle();
				if(!is_array($masterprovince)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $masterprovince);
			}
			
		}
	}
	
 ?>