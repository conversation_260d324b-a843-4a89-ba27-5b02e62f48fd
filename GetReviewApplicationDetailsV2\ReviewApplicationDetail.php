<?php 
	include_once '../Lib/jwt.php';
	include_once 'restReviewApplicationDetail.php';
	
	class ReviewApplicationDetail extends RestReviewApplication {
		
		public function __construct() {
			parent::__construct();
		}

		
		
		public function getReviewApplicationDetails() {
			
			if(!isset($_GET['ApplicationID']) || $_GET['ApplicationID'] == "") {
				
				 $ApplicationId = $this->validateParameter('ApplicationID', $this->branch, STRING);
				
				$ReviewApplicationModel = new ReviewApplicationModel;
				$ReviewApplicationModel->setApplicationId($ApplicationId);
				$ReviewApplicationModel = $ReviewApplicationModel->getAllReviewApplication();
				$this->returnResponseGetAll($ReviewApplicationModel);		
				//$this->returnResponse(SUCCESS_RESPONSE, $ReviewApplicationModel);				
				
			}else{
			
				$ApplicationID = $this->validateParameter('ApplicationID', $_GET['ApplicationID'], STRING);
			
				$ReviewApplicationModel = new ReviewApplicationModel;
				$ReviewApplicationModel->setApplicationID($ApplicationID);
				
				$ReviewApplicationModel = $ReviewApplicationModel->getReviewApplicationByTitle();
				if(!is_array($ReviewApplicationModel)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $ReviewApplicationModel);
			}
			
		}
	}
	
 ?>