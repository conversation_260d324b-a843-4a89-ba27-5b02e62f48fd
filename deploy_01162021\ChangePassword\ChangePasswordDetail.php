<?php 
	include_once '../Lib/jwt.php';
	include_once 'restChangePasswordDetail.php';
	
	class ChangePasswordDetail extends RestChangePassword {
		
		public function __construct() {
			parent::__construct();
		}

		public function ChangePassword() {
			
			
			$data = json_decode($this->request, true);
				
			$OldPassword = $this->validateParameter('OldPassword', $data['OldPassword'], STRING);
			$NewPassword = $this->validateParameter('NewPassword', $data['NewPassword'], STRING);
			$pass = sha1(md5(htmlspecialchars($OldPassword,ENT_QUOTES)));
			$NewPassword1 = sha1(md5(htmlspecialchars($NewPassword,ENT_QUOTES)));
				
				$stmt = $this->dbConn->prepare("SELECT UserID,UserName,Password FROM tbluser WHERE UserID= :UserID and Password=:password");
				$stmt->bindParam(":UserID", $this->userId);
				$stmt->bindParam(":password", $pass);
				$stmt->execute();
				$user = $stmt->fetch(PDO::FETCH_ASSOC);
				
				if(!is_array($user)) {
					http_response_code(404);
					$this->throwError('Failed', "Old Password is not match.");
				}	
				
				$lvm = new ChangePasswordModel;
				$lvm->setPassword($NewPassword1);
				$lvm->setUserID($this->userId);
			
				if(!$lvm->updatePassword()) {
					
					 $message = 'Failed to change password.';
				} else {
								
					$message = 'Password successful change.';
					  
				}
			
			  $this->returnResponse(SUCCESS_RESPONSE, $message);			
		}
		
	}
	
 ?>