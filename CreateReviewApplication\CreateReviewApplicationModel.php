<?php 
include_once '../Lib/DbConnect.php';

	class CreateReviewApplicationModel {
		
		public $tableName = 'Mc_loan_application';
		public $dbConn;
		
		public $SysId;
		public $Created;
		public $Creator;
		public $LastModified;
		public $Modifier;
		public $ApplicationId;
		public $ClientInfoId;
		public $EngineNumber;
		public $ChassisNumber;
		public $ColorOfUnit;
		

		
		function setChassisNumber($ChassisNumber) { $this->ChassisNumber = $ChassisNumber; }
		function getChassisNumber() { return $this->ChassisNumber; }
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setApplicationId($ApplicationId) { $this->ApplicationId = $ApplicationId; }
		function getApplicationId() { return $this->ApplicationId; }
		
		
		function setClientInfoId($ClientInfoId) { $this->ClientInfoId = $ClientInfoId; }
		function getClientInfoId() { return $this->ClientInfoId; }
		
		function setEngineNumber($EngineNumber) { $this->EngineNumber = $EngineNumber; }
		function getEngineNumber() { return $this->EngineNumber; }
		
		function setColorOfUnit($ColorOfUnit) { $this->ColorOfUnit = $ColorOfUnit; }
		function getColorOfUnit() { return $this->ColorOfUnit; }
		
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
		
		public function updateMcApplication() {
			
			$sql = 'update Mc_loan_application set LastModified=:LastModified,Modifier=:Modifier,EngineNumber=:EngineNumber,
													ChassisNumber=:ChassisNumber
													where ApplicationID =:ApplicationId';
													
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':LastModified', $this->LastModified);
			$stmt->bindParam(':Modifier', $this->Modifier);
			$stmt->bindParam(':EngineNumber', $this->EngineNumber);
			$stmt->bindParam(':ChassisNumber', $this->ChassisNumber);
			$stmt->bindParam(':ApplicationId', $this->ApplicationId);
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>