<?php 

	class Api extends Rest {
		
		public function __construct() {
			parent::__construct();
		}

		public function generateToken() {
			
			$email = $this->validateParameter('email', $this->data['email'], STRING);
			$pass1 = $this->validateParameter('pass', $this->data['pass'], STRING);
			$pass= hash('sha512', hash('sha256', md5(htmlspecialchars($pass1,ENT_QUOTES))));
			
			try {
				
				$stmt = $this->dbConn->prepare("SELECT * FROM user_account WHERE email = :email AND CurrentPassword = :pass");
				$stmt->bindParam(":email", $email);
				$stmt->bindParam(":pass", $pass);
				$stmt->execute();
				$user = $stmt->fetch(PDO::FETCH_ASSOC);
				if(!is_array($user)) {
					$this->returnResponse(INVALID_USER_PASS, "Email or Password is incorrect.");
				}

				if( $user['Status'] != "ACTIVE") {
					$this->returnResponse(USER_NOT_ACTIVE, "User is not activated. Please contact to admin.");
				}

				$paylod = [
					'iat' => time(),
					'iss' => 'localhost',
					'exp' => time() + (15*60),
					'userId' => $user['SysId']
				];

				$token = JWT::encode($paylod, SECRETE_KEY);
				
				$data = ['token' => $token];
				$this->returnResponse(SUCCESS_RESPONSE, $data);
			} catch (Exception $e) {
				$this->throwError(JWT_PROCESSING_ERROR, $e->getMessage());
			}
		}
	}
	
 ?>