<?php 
include_once '../Lib/DbConnect.php';

	class client_indv_soi_remittance {
		
		
		private $tableName = 'client_indv_soi_remittance';
		private $dbConn;
		
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $ParentId;
		private $Sender;
		private $Relationship;
		private $Country;
		private $Amount;
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setParentId($ParentId) { $this->ParentId = $ParentId; }
		function getParentId() { return $this->ParentId; }
		
		function setSender($Sender) { $this->Sender = $Sender; }
		function getSender() { return $this->Sender; }
		
		function setRelationship($Relationship) { $this->Relationship = $Relationship; }
		function getRelationship() { return $this->Relationship; }
		
		function setCountry($Country) { $this->Country = $Country; }
		function getCountry() { return $this->Country; }
		
		function setAmount($Amount) { $this->Amount = $Amount; }
		function getAmount() { return $this->Amount; }
		
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
				
		public function client_indv_soi_remittance() {
			
			$sql = 'INSERT INTO  client_indv_soi_remittance (Creator,Created,ParentId,Sender,Relationship,Country,Amount) 
														VALUES (:Creator,:Created,:ParentId,:Sender,:Relationship,:Country,:Amount)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':ParentId', $this->ParentId);
			$stmt->bindParam(':Sender', $this->Sender);
			$stmt->bindParam(':Relationship', $this->Relationship);
			$stmt->bindParam(':Country', $this->Country);
			$stmt->bindParam(':Amount', $this->Amount);
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>