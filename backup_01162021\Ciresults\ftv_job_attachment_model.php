<?php 
include_once '../Lib/DbConnect.php';

	class ftv_job_attachment_model {
		
		
		private $tableName = 'ftv_job_attachment';
		private $dbConn;
	
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $JobId;
		private $Category;
		private $ProofImage;
		private $Remarks;	
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setProofImage($ProofImage) { $this->ProofImage = $ProofImage; }
		function getProofImage() { return $this->ProofImage; }
			
		function setRemarks($Remarks) { $this->Remarks = $Remarks; }
		function getRemarks() { return $this->Remarks; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setJobId($JobId) { $this->JobId = $JobId; }
		function getJobId() { return $this->JobId; }
				
		function setCategory($Category) { $this->Category = $Category; }
		function getCategory() { return $this->Category; }
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
		

				
		public function ftv_job_attachment() {
			
			$sql = 'INSERT INTO  ftv_job_attachment (Creator,Created,JobId,Category,ProofImage,Remarks) 
								 VALUES(:Creator,:Created,:JobId,:Category,:ProofImage,:Remarks)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':JobId', $this->JobId);
			$stmt->bindParam(':Category', $this->Category);
			$stmt->bindParam(':ProofImage', $this->ProofImage);
			$stmt->bindParam(':Remarks', $this->Remarks);
	
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>