<?php 
include_once '../Lib/DbConnect.php';

	class MasterCityModel {
		
		public $tableName = 'master_city';
		public $dbConn;
			
		
		public $SysId;
		public $Created;
		public $Creator;
		public $LastModified;
		public $Modifier;
		public $MappingId;
		public $Title;
		public $OrderNumber;
		public $IncomeClass;
		public $ParentId;
	
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setMappingId($MappingId) { $this->MappingId = $MappingId; }
		function getMappingId() { return $this->MappingId; }
		
		
		function setTitle($Title) { $this->Title = $Title; }
		function getTitle() { return $this->Title; }
		
		function setOrderNumber($OrderNumber) { $this->OrderNumber = $OrderNumber; }
		function getOrderNumber() { return $this->OrderNumber; }
		
		function setParentId($ParentId) { $this->ParentId = $ParentId; }
		function getParentId() { return $this->ParentId; }
		
		function setIncomeClass($IncomeClass) { $this->IncomeClass = $IncomeClass; }
		function getIncomeClass() { return $this->IncomeClass; }
		
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		public function getMasterCityByTitle() {
					
					
					$sql = "select * from master_city a where a.Title = :Title";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':Title', $this->Title);
					$stmt->execute();
					$mastercity = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $mastercity;
					
		}
		
		public function getAllMasterCity() {
					
					$sql = "select * from master_city order by OrderNumber ";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->execute();
					$mastercity = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $mastercity;
		}
		
		public function updateCM() {
			
			$sql = 'update master_city set LastModified=:LastModified,Modifier=:Modifier,MappingId=:MappingId,
													Title=:Title,OrderNumber=:OrderNumber,IncomeClass=:IncomeClass,
													ParentId=:ParentId where SysId =:SysId';
			
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':LastModified', $this->LastModified);
			$stmt->bindParam(':Modifier', $this->Modifier);
			$stmt->bindParam(':MappingId', $this->MappingId);;
			$stmt->bindParam(':Title', $this->Title);
			$stmt->bindParam(':OrderNumber', $this->OrderNumber);
			$stmt->bindParam(':IncomeClass', $this->IncomeClass);
			$stmt->bindParam(':ParentId', $this->ParentId);
			$stmt->bindParam(':SysId', $this->SysId);
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
				
		public function insertCM() {
			

			$sql = 'INSERT INTO  master_city (Creator,Created,MappingId,Title,ParentId,IncomeClass,OrderNumber) 
												VALUES(:Creator,:Created,:MappingId,:Title,:ParentId,:IncomeClass,:OrderNumber)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':MappingId', $this->MappingId);
			$stmt->bindParam(':Title', $this->Title);
			$stmt->bindParam(':ParentId', $this->ParentId);
			$stmt->bindParam(':IncomeClass', $this->IncomeClass);
			$stmt->bindParam(':OrderNumber', $this->OrderNumber);
			
			if($stmt->execute()) {				 
			
				 // $SysId = $this->dbConn->lastInsertId();
				 // return $SysId;
				 
				return true;
				
			} else {
				return false;
			}
		}
	}
 ?>