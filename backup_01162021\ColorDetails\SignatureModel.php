<?php 
include_once '../Lib/DbConnect.php';

	class SignatureModel {
		
		
		public $ApplicationId;
	
		function setApplicationId($ApplicationId) { $this->ApplicationId = $ApplicationId; }
		function getApplicationId() { return $this->ApplicationId; }
		
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
		
		public function getAllSignature() {
					
					$sql = "SELECT A.SysId, B.Color color_acu_id, B.Description color_description, a.AcuInvId model_acu_id
							FROM tblinvcolor A
							LEFT JOIN tblcolor B ON A.ColorId=B.ColorId
							LEFT JOIN DealerPricing c on a.AcuInvId = c.acu_InvId
							WHERE a.AcuInvId IS NOT NULL and a.status = 1";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->execute();
					$Signature = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $Signature;
					
		}
	}
 ?>