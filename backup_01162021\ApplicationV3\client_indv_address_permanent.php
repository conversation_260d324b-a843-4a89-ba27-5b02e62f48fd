<?php 
include_once '../Lib/DbConnect.php';

	class client_indv_address_permanent {
		
		
		private $tableName = 'client_indv_address_permanent';
		private $dbConn;

		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $ClientId;
		private $BlkNumber;
		private $Street;
		private $Subdivision;
		private $Barangay;
		private $City;
		private $Province;
		private $Types;
		private $Duration;
		private $Zipcode;
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setClientId($ClientId) { $this->ClientId = $ClientId; }
		function getClientId() { return $this->ClientId; }
		
		function setBlkNumber($BlkNumber) { $this->BlkNumber = $BlkNumber; }
		function getBlkNumber() { return $this->BlkNumber; }
		
		function setStreet($Street) { $this->Street = $Street; }
		function getStreet() { return $this->Street; }
		
		function setSubdivision($Subdivision) { $this->Subdivision = $Subdivision; }
		function getSubdivision() { return $this->Subdivision; }
		
		function setBarangay($Barangay) { $this->Barangay = $Barangay; }
		function getBarangay() { return $this->Barangay; }
		
		function setCity($City) { $this->City = $City; }
		function getCity() { return $this->City; }
		
		function setProvince($Province) { $this->Province = $Province; }
		function getProvince() { return $this->Province; }
		
		function setTypes($Types) { $this->Types = $Types; }
		function getTypes() { return $this->Types; }
		
		function setDuration($Duration) { $this->Duration = $Duration; }
		function getDuration() { return $this->Duration; }
		
		function setZipcode($Zipcode) { $this->Zipcode = $Zipcode; }
		function getZipcode() { return $this->Zipcode; }
		
	
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
				
		public function client_indv_address_permanent() {
			
			$sql = 'INSERT INTO  client_indv_address_permanent (Creator,Created,ClientId,BlkNumber,Street,Subdivision,Barangay,City,Province,Type,Duration) 
														VALUES(:Creator,:Created,:ClientId,:BlkNumber,:Street,:Subdivision,:Barangay,:City,:Province,:Types,:Duration)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':ClientId', $this->ClientId);
			$stmt->bindParam(':BlkNumber', $this->BlkNumber);
			$stmt->bindParam(':Street', $this->Street);
			$stmt->bindParam(':Subdivision', $this->Subdivision);
			$stmt->bindParam(':Barangay', $this->Barangay);
			$stmt->bindParam(':City', $this->City);
			$stmt->bindParam(':Province', $this->Province);
			$stmt->bindParam(':Types', $this->Types);
			$stmt->bindParam(':Duration', $this->Duration);
			//$stmt->bindParam(':Zipcode', $this->Zipcode);
						
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>