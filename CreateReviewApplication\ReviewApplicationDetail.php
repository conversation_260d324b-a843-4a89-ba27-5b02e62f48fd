<?php 
	include_once '../Lib/jwt.php';
	include_once 'restReviewApplicationDetail.php';
	
	class ReviewApplicationDetail extends RestReviewApplication {
		
		public function __construct() {
			parent::__construct();
		}

		public function addReviewApplication() {
			
			
			// if(!isset($_GET['ChassisNumber']) || $_GET['ChassisNumber'] == "") {
				 // $message = 'ChassisNumber is required.';
			// }
			
			// if(!isset($_GET['EngineNumber']) || $_GET['EngineNumber'] == "") {
				 // $message = 'EngineNumber is required.';
			// }
			
			// if(!isset($_GET['ColorOfUnit']) || $_GET['ColorOfUnit'] == "") {
				// $message = 'ColorOfUnit is required.';
			// }
			
			if(!isset($_GET['SysId']) || $_GET['SysId'] == "") {
				$message = 'SysId is required.';
			}
			if(!isset($_GET['ApplicationId']) || $_GET['ApplicationId'] == "") {
				$message = 'ApplicationId is required.';
			}
		
			
			
			$ChassisNumber = $this->validateParameter('ChassisNumber', $_GET['ChassisNumber'], STRING, false);
			$EngineNumber = $this->validateParameter('EngineNumber', $_GET['EngineNumber'], STRING, false);
			//$ColorOfUnit = $this->validateParameter('ColorOfUnit', $_GET['ColorOfUnit'], STRING);
			$SysId = $this->validateParameter('SysId', $_GET['SysId'], STRING, true);
			$ApplicationId = $this->validateParameter('ApplicationId', $_GET['ApplicationId'], STRING, true);
			
			if( $ChassisNumber == "" && $EngineNumber == ""){
				
				$this->Additional_Mc_loan_attachment($SysId);
				

			}else{
			
				$mrm = new CreateReviewApplicationModel;
				$mrm->setChassisNumber($ChassisNumber);
				$mrm->setEngineNumber($EngineNumber);
				//$mrm->setColorOfUnit($ColorOfUnit);
				$mrm->setSysId($SysId);
				$mrm->setApplicationId($ApplicationId);
				$mrm->setLastModified(date('Y-m-d'));
				$mrm->setModifier($this->userId);
				
				
				if(!$mrm->updateMcApplication()) {
					
					 $message = 'Failed to insert.';
				} else {
								
					$this->Additional_Mc_loan_attachment($SysId);
				//	$message = 'Inserted successful.';
					  
				}
			}
			
		 
			  //$this->returnResponse(SUCCESS_RESPONSE, $mrm);
		}
		
		public function Additional_Mc_loan_attachment($SysId) {
			
			
			
			$data = json_decode($this->request, true);
			
			
			
			
			//$data = $this->mc_loan_attachment;
			foreach($data as $obj){
				
			$DocType = $this->validateParameter('DocType', $obj['docType'], STRING, false);
			$ImageBase64 = $this->validateParameter('ImageBase64', $obj['imageBase64'], STRING, FALSE);
			$Remarks = $this->validateParameter('Remarks', $obj['remarks'], STRING, false);
			
			if($DocType=='PHOTO - CLIENT'){
				$Mc_loan_attachment = new Mc_loan_attachment;
				$Mc_loan_attachment->setParentId($SysId);
				$Mc_loan_attachment->setDocType($DocType);
				$Mc_loan_attachment->setImageBase64($ImageBase64);
				$Mc_loan_attachment->setRemarks($Remarks);
				
				
				if(!$Mc_loan_attachment->update_Mc_loan_attachment()) {
					
					 $message = 'Failed to update.';
				} else {
								
					//$this->Additional_Mc_loan_attachment($SysId);
				//	$message = 'Inserted successful.';
					  
				}
			// $this->returnResponse(SUCCESS_RESPONSE, $DocType);
			// return false;
			}else{
			$Mc_loan_attachment = new Mc_loan_attachment;
			$Mc_loan_attachment->setCreated(date('Y-m-d h:i:s'));
			$Mc_loan_attachment->setCreator($this->userId);
			$Mc_loan_attachment->setParentId($SysId);
			$Mc_loan_attachment->setDocType($DocType);
			$Mc_loan_attachment->setImageBase64($ImageBase64);
			$Mc_loan_attachment->setRemarks($Remarks);
			
			
			if(!$Mc_loan_attachment->Mc_loan_attachment()) {
				
				 // $message = 'Failed to insert.';
				} else {
								
					// $message = 'Inserted successful.';
					  
					// $message = $lvm->insertLV();
				}
			}
			
		  }
			  // $this->returnResponse(SUCCESS_RESPONSE, $message);
		}
	}
	
 ?>