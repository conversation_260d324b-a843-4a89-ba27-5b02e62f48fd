<?php 
	include_once '../Lib/jwt.php';
	include_once 'restCustDetail.php';
	
	class CustDetail extends RestCustomer {
		
		public function __construct() {
			parent::__construct();
		}

		public function addCustomer() {
			
		
			$datas = json_decode($this->request, true);
			
			foreach($datas as $obj){
				
			$FirstName = $this->validateParameter('FirstName', $obj['FirstName'], STRING, false);
			$MiddleName = $this->validateParameter('MiddleName', $obj['MiddleName'], STRING, false);
			$LastName = $this->validateParameter('LastName', $obj['LastName'], STRING, false);
			$MobileNumber = $this->validateParameter('MobileNumber', $obj['MobileNumber'], STRING, false);
			$SuffixName = $this->validateParameter('SuffixName', $obj['SuffixName'], STRING, false);
			$DOB = $this->validateParameter('DOB', $obj['DOB'], STRING, false);
			$Nationality  = $this->validateParameter('Nationality', $obj['Nationality'], STRING, false);
			$Religion  = $this->validateParameter('Religion', $obj['Religion'], STRING, false);
			$ApplicationID  = $this->validateParameter('ApplicationID', $obj['ApplicationID'], STRING, false);
			$CivilStatus  = $this->validateParameter('CivilStatus', $obj['CivilStatus'], STRING, false);
			$Gender  = $this->validateParameter('Gender', $obj['Gender'], STRING, false);
			$Email = $this->validateParameter('Email', $obj['Email'], STRING, false);
			// $Created = $this->validateParameter('Created', $obj['Created'], STRING, false);
			// $Creator = $this->validateParameter('Creator', $obj['Creator'], STRING, false);
			$Source = $this->validateParameter('Source', $obj['Source'], STRING, false);
			$BirthPlace= $this->validateParameter('BirthPlace', $obj['BirthPlace'], STRING, false);
			$Employer= $this->validateParameter('Employer', $obj['Employer'], STRING, false);
			$Position= $this->validateParameter('Position', $obj['Position'], STRING, false);
			$Branch= $this->validateParameter('Branch', $obj['Branch'], STRING, false);

			$PerAddBlkNumber= $this->validateParameter('PerAddBlkNumber', $obj['PerAddBlkNumber'], STRING, false);
			$PerAddStreet= $this->validateParameter('PerAddStreet', $obj['PerAddStreet'], STRING, false);
			$PerAddBarangay= $this->validateParameter('PerAddBarangay', $obj['PerAddBarangay'], STRING, false);
			$PerAddCity= $this->validateParameter('PerAddCity', $obj['PerAddCity'], STRING, false);
			$PerAddProvince= $this->validateParameter('PerAddProvince', $obj['PerAddProvince'], STRING, false);
			$PerAddZipCode= $this->validateParameter('PerAddZipCode', $obj['PerAddZipCode'], STRING, false);

			$CurrAddBlkNumber= $this->validateParameter('CurrAddBlkNumber', $obj['CurrAddBlkNumber'], STRING, false);
			$CurrAddStreet= $this->validateParameter('CurrAddStreet', $obj['CurrAddStreet'], STRING, false);
			$CurrAddBarangay= $this->validateParameter('CurrAddBarangay', $obj['CurrAddBarangay'], STRING, false);
			$CurrAddCity= $this->validateParameter('CurrAddCity', $obj['CurrAddCity'], STRING, false);
			$CurrAddProvince= $this->validateParameter('CurrAddProvince', $obj['CurrAddProvince'], STRING, false);
			$CurrAddZipCode= $this->validateParameter('CurrAddZipCode', $obj['CurrAddZipCode'], STRING, false);

			$cust = new ClientInfoModel;
			$cust->setFirstName($FirstName);
			$cust->setMiddleName($MiddleName);
			$cust->setLastName($LastName);
			$cust->setMobileNumber($MobileNumber);
			$cust->setSuffixName($SuffixName);
			$cust->setDOB($DOB);
			$cust->setNationality($Nationality);
			$cust->setReligion($Religion);
			$cust->setApplicationID($ApplicationID);
			$cust->setCivilStatus($CivilStatus);
			$cust->setGender($Gender);
			$cust->setEmail($Email);
			$cust->setCreated(date('Y-m-d'));
			$cust->setCreator($this->userId);
			$cust->setSource($Source);
			$cust->setBirthPlace($BirthPlace);
			$cust->setEmployer($Employer);
			$cust->setPosition($Position);
			$cust->setBranch($Branch);
			$cust->setPerAddBlkNumber($PerAddBlkNumber);
			$cust->setPerAddStreet($PerAddStreet);
			$cust->setPerAddBarangay($PerAddBarangay);
			$cust->setPerAddCity($PerAddCity);
			$cust->setPerAddProvince($PerAddProvince);
			$cust->setPerAddZipCode($PerAddZipCode);
			$cust->setCurrAddBlkNumber($CurrAddBlkNumber);
			$cust->setCurrAddStreet($CurrAddStreet);
			$cust->setCurrAddBarangay($CurrAddBarangay);
			$cust->setCurrAddCity($CurrAddCity);
			$cust->setCurrAddProvince($CurrAddProvince);
			$cust->setCurrAddZipCode($CurrAddZipCode);
			 if(!$cust->insert()) {
				$message = 'Failed to insert.';
			} else {
				$message = "Inserted successfully.";
			}
			
		  }
			$this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function getCustomerDetailsById() {
			
			if(!isset($_GET['id']) || $_GET['id'] == "") {
				
				$cust = new ClientInfoModel;
				$response1 = array();
				$customer = $cust->getAllCustomer();
				
				// foreach( $customer as $obj){
					
				// $response['FirstName'] 				= $obj['FirstName'];
				// $response['MiddleName'] 			= $obj['MiddleName'];
				// $response['LastName'] 				= $obj['LastName'];
				// $response['MobileNumber'] 			= $obj['MobileNumber'];
				// $response['SuffixName'] 			= $obj['SuffixName'];
				// $response['DOB'] 					= $obj['DOB'];
				// array_push($response1,$response);

				 // }
				$this->returnResponseGetAll($customer);				
				
			}else{
			
				$customerId = $this->validateParameter('customerId', $_GET['id'], INTEGER);
			
				$cust = new ClientInfoModel;
				$cust->setSysId($customerId);
				
		
				$customer = $cust->getCustomerDetailsById();
				if(!is_array($customer)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'Customer details not found.']);
				}
								

				// $response['FirstName'] 				= $customer['FirstName'];
				// $response['MiddleName'] 			= $customer['MiddleName'];
				// $response['LastName'] 				= $customer['LastName'];
				// $response['MobileNumber'] 			= $customer['MobileNumber'];
				// $response['SuffixName'] 			= $customer['SuffixName'];
				// $response['DOB'] 					= $customer['DOB'];
				
				$this->returnResponse(SUCCESS_RESPONSE, $customer);
			
			}
			
		}
	}
	
 ?>