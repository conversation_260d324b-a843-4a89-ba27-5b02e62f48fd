<?php 
include_once '../Lib/DbConnect.php';

	class client_indv_spouse {
		
		
		private $tableName = 'client_indv_spouse';
		private $dbConn;
		
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $FirstName;
		private $MiddleName;
		private $LastName;
		private $SuffixName;
		private $DOB;
		private $ClientId;
		private $Gender;
		private $MobileNumber;
		private $PhoneNumber;
		private $Email;
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setFirstName($FirstName) { $this->FirstName = $FirstName; }
		function getFirstName() { return $this->FirstName; }
		
		function setMiddleName($MiddleName) { $this->MiddleName = $MiddleName; }
		function getMiddleName() { return $this->MiddleName; }
		
		function setLastName($LastName) { $this->LastName = $LastName; }
		function getLastName() { return $this->LastName; }
		
		function setSuffixName($SuffixName) { $this->SuffixName = $SuffixName; }
		function getSuffixName() { return $this->SuffixName; }
		
		function setDOB($DOB) { $this->DOB = $DOB; }
		function getDOB() { return $this->DOB; }
		
		function setClientId($ClientId) { $this->ClientId = $ClientId; }
		function getClientId() { return $this->ClientId; }
		
		function setGender($Gender) { $this->Gender = $Gender; }
		function getGender() { return $this->Gender; }
		
		function setMobileNumber($MobileNumber) { $this->MobileNumber = $MobileNumber; }
		function getMobileNumber() { return $this->MobileNumber; }
		
		function setPhoneNumber($PhoneNumber) { $this->PhoneNumber = $PhoneNumber; }
		function getPhoneNumber() { return $this->PhoneNumber; }
		
		function setEmail($Email) { $this->Email = $Email; }
		function getEmail() { return $this->Email; }

		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
				
		public function client_indv_spouse() {
					
			$sql = 'INSERT INTO  client_indv_spouse (Creator,Created,FirstName,MiddleName,LastName,SuffixName,DOB,ClientId,MobileNumber,PhoneNumber,Email) 
											 VALUES (:Creator,:Created,:FirstName,:MiddleName,:LastName,:SuffixName,:DOB,:ClientId,:MobileNumber,:PhoneNumber,:Email)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':FirstName', $this->FirstName);
			$stmt->bindParam(':MiddleName', $this->MiddleName);
			$stmt->bindParam(':LastName', $this->LastName);
			$stmt->bindParam(':SuffixName', $this->SuffixName);
			$stmt->bindParam(':DOB', $this->DOB);
			$stmt->bindParam(':ClientId', $this->ClientId);
			$stmt->bindParam(':MobileNumber', $this->MobileNumber);
			$stmt->bindParam(':PhoneNumber', $this->PhoneNumber);
			$stmt->bindParam(':Email', $this->Email);
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>