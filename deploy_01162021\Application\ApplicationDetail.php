<?php 
	include_once '../Lib/jwt.php';
	include_once 'restApplicationDetail.php';
	
	class ApplicationDetail extends RestApplication {
		
		public function __construct() {
			parent::__construct();
		}
		
		public function Validate_data(){
			
			$this->data = json_decode($this->request, true);
			
			//client information
			$FirstName = $this->validateParameter('FirstName', $this->client_personal_info['firstName'], STRING);
			$MiddleName = $this->validateParameter('MiddleName', $this->client_personal_info['middleName'], STRING, FALSE);
			$LastName = $this->validateParameter('LastName', $this->client_personal_info['lastName'], STRING);
			$SuffixName = $this->validateParameter('SuffixName', $this->client_personal_info['suffixName'], STRING, FALSE);
			$DOB = $this->validateParameter('DOB', $this->client_personal_info['dob'], STRING);
			$Gender = $this->validateParameter('Gender', $this->client_personal_info['gender'], STRING);
			$BirthPlace = $this->validateParameter('BirthPlace', $this->client_personal_info['birthPlace'], STRING);
			$Nationality = $this->validateParameter('Nationality', $this->client_personal_info['nationality'], STRING);
			$CivilStatus = $this->validateParameter('CivilStatus', $this->client_personal_info['civilStatus'], STRING);
			$MobileNumber = $this->validateParameter('MobileNumber', $this->client_personal_info['mobileNumber'], STRING);
			$PhoneNumber = $this->validateParameter('PhoneNumber', $this->client_personal_info['phoneNumber'], STRING , FALSE);
			$Email = $this->validateParameter('Email', $this->client_personal_info['email'], STRING);
			$EducationalAttainment = $this->validateParameter('EducationalAttainment', $this->client_personal_info['educationalAttainment'], STRING);
			$Beneficiary = $this->validateParameter('Beneficiary', $this->client_personal_info['beneficiary'], STRING);
			
			if($this->client_personal_info['civilStatus'] == "SINGLE"){}else{
			//client spouse information
			$FirstName = $this->validateParameter('FirstName', $this->client_indv_spouse['firstName'], STRING);
			$MiddleName = $this->validateParameter('MiddleName', $this->client_indv_spouse['middleName'], STRING, FALSE);
			$LastName = $this->validateParameter('LastName', $this->client_indv_spouse['lastName'], STRING);
			$SuffixName = $this->validateParameter('SuffixName', $this->client_indv_spouse['suffixName'], STRING, FALSE);
			$DOB = $this->validateParameter('DOB', $this->client_indv_spouse['dob'], STRING);
			$MobileNumber = $this->validateParameter('MobileNumber', $this->client_indv_spouse['mobileNumber'], STRING);
			//$PhoneNumber = $this->validateParameter('PhoneNumber', $this->client_indv_spouse['phoneNumber'], STRING, FALSE);
			$Email = $this->validateParameter('Email', $this->client_indv_spouse['email'], STRING, FALSE);
			}
			//Mc loan Application
			//$ApplicationId = $this->validateParameter('ApplicationId',  $this->data['loanApplicationId'], INTEGER);
			$ApplicationDate = $this->validateParameter('ApplicationDate',  $this->data['applicationDate'], STRING);
			$Brand = $this->validateParameter('Brand',  $this->data['brand'], STRING);
			$Channel = $this->validateParameter('Channel',  $this->data['channel'], STRING);
			$Classification = $this->validateParameter('Classification',  $this->data['classification'], STRING);
			$Crsc = $this->validateParameter('Crsc',  $this->data['crsc'], STRING, FALSE);
			$DateTimeOnBoardStarted = $this->validateParameter('DateTimeOnBoardStarted',  $this->data['dateTimeOnBoardStarted'], STRING);
			$DealerCompany = $this->validateParameter('DealerCompany',  $this->data['dealerCompany'], STRING);
			$DealerKey = $this->validateParameter('DealerKey',  $this->data['dealerKey'], STRING);
			$DownPayment = $this->validateParameter('DownPayment',  $this->data['downPayment'], INTEGER);
			$FinancingBranch = $this->validateParameter('FinancingBranch',  $this->data['financingBranch'], STRING);
			$IsRepeatBorrower = $this->validateParameter('IsRepeatBorrower',  $this->data['isRepeatBorrower'], BOOLEAN,FALSE);
			$LoanType = $this->validateParameter('LoanType',  $this->data['loanType'], STRING);
			$Purpose = $this->validateParameter('Purpose',  $this->data['purpose'], STRING);
			$Rebate = $this->validateParameter('Rebate',  $this->data['rebate'], INTEGER);
			$MonthlyAmort = $this->validateParameter('MonthlyAmort',  $this->data['monthlyAmort'], INTEGER);
			$Term = $this->validateParameter('Term',  $this->data['term'], INTEGER);
			$SellingPrice = $this->validateParameter('SellingPrice',  $this->data['sellingPrice'], INTEGER);
			$Model = $this->validateParameter('Model',  $this->data['model'], STRING);
			$NumberOfUnit = $this->validateParameter('NumberOfUnit',  $this->data['numberOfUnit'], INTEGER);
			$DealerBranch = $this->validateParameter('DealerBranch',  $this->data['dealerBranchCode'], STRING);
			
			//current address
			$Barangay = $this->validateParameter('Barangay', $this->client_address_current['barangay'], STRING);
			$BlkNumber = $this->validateParameter('BlkNumber', $this->client_address_current['blkNumber'], STRING);
			$City = $this->validateParameter('City', $this->client_address_current['city'], STRING);
			$Duration = $this->validateParameter('Duration', $this->client_address_current['duration'], STRING);
			$Province = $this->validateParameter('Province', $this->client_address_current['province'], STRING);
			$Street = $this->validateParameter('Street', $this->client_address_current['street'], STRING);
			$Type = $this->validateParameter('Type', $this->client_address_current['type'], STRING);
			
			
			//permanent address
			$Barangay = $this->validateParameter('Barangay', $this->client_address_permanent['barangay'], STRING);
			$BlkNumber = $this->validateParameter('BlkNumber', $this->client_address_permanent['blkNumber'], STRING);
			$City = $this->validateParameter('City', $this->client_address_permanent['city'], STRING);
			$Duration = $this->validateParameter('Duration', $this->client_address_permanent['duration'], STRING);
			$Province = $this->validateParameter('Province', $this->client_address_permanent['province'], STRING);
			$Street = $this->validateParameter('Street', $this->client_address_permanent['street'], STRING);
			$Type = $this->validateParameter('Type', $this->client_address_permanent['type'], STRING);
			
			//family expenditure
			$Food = $this->validateParameter('Food', $this->client_family_expenditure['food'], INTEGER);
			$EducAllowance = $this->validateParameter('EducAllowance', $this->client_family_expenditure['educAllowance'], INTEGER);
			$EducTuitionFeePublic = $this->validateParameter('EducTuitionFeePublic', $this->client_family_expenditure['educTuitionFeePublic'], INTEGER);
			$EducTuitionFeePrivate = $this->validateParameter('EducTuitionFeePrivate', $this->client_family_expenditure['educTuitionFeePrivate'], INTEGER);
			$Electricity = $this->validateParameter('Electricity', $this->client_family_expenditure['electricity'], INTEGER);
			$Water = $this->validateParameter('Water', $this->client_family_expenditure['water'], INTEGER);
			$ELoad = $this->validateParameter('ELoad', $this->client_family_expenditure['eLoad'], INTEGER);
			$CableTV = $this->validateParameter('CableTV', $this->client_family_expenditure['cableTV'], INTEGER);
			$Internet = $this->validateParameter('Internet', $this->client_family_expenditure['internet'], INTEGER);
			$Transportation = $this->validateParameter('Transportation', $this->client_family_expenditure['transportation'], INTEGER);
			$Medical = $this->validateParameter('Medical', $this->client_family_expenditure['medical'], INTEGER);
			$ExistingObligation = $this->validateParameter('ExistingObligation', $this->client_family_expenditure['existingObligation'], INTEGER);
			$HouseRent = $this->validateParameter('HouseRent', $this->client_family_expenditure['houseRent'], INTEGER);
			$Utilities = $this->validateParameter('Utilities', $this->client_family_expenditure['utilities'], INTEGER);
			$Others = $this->validateParameter('Others', $this->client_family_expenditure['others'], INTEGER);
			$Miscellaneous = $this->validateParameter('Miscellaneous', $this->client_family_expenditure['miscellaneous'], INTEGER);
			
			//client_character_reference
			
			$client_character_reference = $this->client_character_reference;
			
			foreach($client_character_reference as $obj){
			$FullName = $this->validateParameter('FullName', $obj['fullName'], STRING, true);
			$Relationship = $this->validateParameter('Relationship', $obj['relationship'], STRING, true);
			$ContactNumber = $this->validateParameter('ContactNumber', $obj['contactNumber'], INTEGER, true);
			
			}
			
			//client_household_details
			
			$client_household_details = $this->client_household_details;
			
			foreach($client_household_details as $obj){
				
			$FullName = $this->validateParameter('FullName', $obj['fullName'], STRING, true);
			$Relationship = $this->validateParameter('Relationship', $obj['relationship'], STRING, true);
			$Age = $this->validateParameter('Age', $obj['age'], INTEGER, true);
			}
			
			//client_identification
			$client_identification = $this->client_identification;
			
			foreach($client_identification as $obj){
				
			$IdNumber = $this->validateParameter('IdNumber', $obj['idNumber'], STRING, true);
			$Type = $this->validateParameter('Type', $obj['type'], STRING, true);
			
			}
			
			//client_indv_source_of_income
			$client_indv_source_of_income = $this->client_indv_source_of_income;
			
			foreach($client_indv_source_of_income as $obj){
				
			$Category = $this->validateParameter('Category', $obj['category'], STRING, true);
			$Type = $this->validateParameter('Type', $obj['type'], STRING, true);
			
			if($Category == "PRIVATE EMPLOYEE"){

				$Barangay = $this->validateParameter('Barangay', $obj['client_indv_soi_private_employee']['barangay'], STRING, true);
				$BldgNumber = $this->validateParameter('BldgNumber', $obj['client_indv_soi_private_employee']['bldgNumber'], STRING, true);
				$City = $this->validateParameter('City', $obj['client_indv_soi_private_employee']['city'], STRING, true);
				$CompanyName = $this->validateParameter('CompanyName', $obj['client_indv_soi_private_employee']['companyName'], STRING, true);
				$ContactNumber = $this->validateParameter('ContactNumber', $obj['client_indv_soi_private_employee']['contactNumber'], STRING, true);
				$ContactPerson = $this->validateParameter('ContactPerson', $obj['client_indv_soi_private_employee']['contactPerson'], STRING, true);
				$EmploymentStatus = $this->validateParameter('EmploymentStatus', $obj['client_indv_soi_private_employee']['employmentStatus'], STRING, true);
				$NatureOfWork = $this->validateParameter('NatureOfWork', $obj['client_indv_soi_private_employee']['natureOfWork'], STRING, true);
				$NetIncome = $this->validateParameter('NetIncome', $obj['client_indv_soi_private_employee']['netIncome'], INTEGER, true);
				$Occupation = $this->validateParameter('Occupation', $obj['client_indv_soi_private_employee']['occupation'], STRING, true);
				$Position = $this->validateParameter('Position', $obj['client_indv_soi_private_employee']['position'], STRING, true);
				$Province = $this->validateParameter('Province', $obj['client_indv_soi_private_employee']['province'], STRING, true);
				$Street = $this->validateParameter('Street', $obj['client_indv_soi_private_employee']['street'], STRING, true);
				$Tenure = $this->validateParameter('Tenure', $obj['client_indv_soi_private_employee']['tenure'], STRING, true);
				
			}else if ($Category =="GOVERNMENT EMPLOYEE"){
				
				$Barangay = $this->validateParameter('Barangay', $obj['client_indv_soi_goverment_employee']['barangay'], STRING, true);
				$BldgNumber = $this->validateParameter('BldgNumber', $obj['client_indv_soi_goverment_employee']['bldgNumber'], STRING, true);
				$City = $this->validateParameter('City', $obj['client_indv_soi_goverment_employee']['city'], STRING, true);
				$CompanyName = $this->validateParameter('CompanyName', $obj['client_indv_soi_goverment_employee']['companyName'], STRING, true);
				$ContactNumber = $this->validateParameter('ContactNumber', $obj['client_indv_soi_goverment_employee']['contactNumber'], STRING, true);
				$ContactPerson = $this->validateParameter('ContactPerson', $obj['client_indv_soi_goverment_employee']['contactPerson'], STRING, true);
				$EmploymentStatus = $this->validateParameter('EmploymentStatus', $obj['client_indv_soi_goverment_employee']['employmentStatus'], STRING, true);
				$NatureOfWork = $this->validateParameter('NatureOfWork', $obj['client_indv_soi_goverment_employee']['natureOfWork'], STRING, true);
				$NetIncome = $this->validateParameter('NetIncome', $obj['client_indv_soi_goverment_employee']['netIncome'], INTEGER, true);
				$Occupation = $this->validateParameter('Occupation', $obj['client_indv_soi_goverment_employee']['occupation'], STRING, true);
				$Position = $this->validateParameter('Position', $obj['client_indv_soi_goverment_employee']['position'], STRING, true);
				$Province = $this->validateParameter('Province', $obj['client_indv_soi_goverment_employee']['province'], STRING, true);
				$Street = $this->validateParameter('Street', $obj['client_indv_soi_goverment_employee']['street'], STRING, true);
				$Tenure = $this->validateParameter('Tenure', $obj['client_indv_soi_goverment_employee']['tenure'], STRING, true);
				
				
			}else if ($Category =="BUSINESS"){
				
				$Barangay = $this->validateParameter('Barangay', $obj['client_indv_soi_business']['barangay'], STRING, true);
				$BldgNumber = $this->validateParameter('BldgNumber', $obj['client_indv_soi_business']['bldgNumber'], STRING, true);
				$City = $this->validateParameter('City', $obj['client_indv_soi_business']['city'], STRING, true);
				$ContactNumber = $this->validateParameter('ContactNumber', $obj['client_indv_soi_business']['contactNumber'], STRING, true);
				$ContactPerson = $this->validateParameter('ContactPerson', $obj['client_indv_soi_business']['contactPerson'], STRING, true);
				$NetIncome = $this->validateParameter('NetIncome', $obj['client_indv_soi_business']['netIncome'], INTEGER, true);
				$Name = $this->validateParameter('Name', $obj['client_indv_soi_business']['name'], STRING, true);
				$Nature = $this->validateParameter('Nature', $obj['client_indv_soi_business']['nature'], STRING, true);
				$Occupation = $this->validateParameter('Occupation', $obj['client_indv_soi_business']['occupation'], STRING, true);
				$Province = $this->validateParameter('Province', $obj['client_indv_soi_business']['province'], STRING, true);
				$Street = $this->validateParameter('Street', $obj['client_indv_soi_business']['street'], STRING, true);
				$YearsOfOperation = $this->validateParameter('YearsOfOperation', $obj['client_indv_soi_business']['yearsOfOperation'], STRING, true);
			
			
				
			}else if ($Category =="REMITTANCE"){
				

				$Amount = $this->validateParameter('Amount', $obj['client_indv_soi_remittance']['amount'], INTEGER, true);
				$Sender = $this->validateParameter('Sender', $obj['client_indv_soi_remittance']['sender'], STRING, true);
				$Country = $this->validateParameter('Country', $obj['client_indv_soi_remittance']['country'], STRING, true);
				$Relationship = $this->validateParameter('Relationship', $obj['client_indv_soi_remittance']['relationship'], STRING, true);
			
				
				
			}else if ($Category =="PENSION"){
				
				
				$Amount = $this->validateParameter('Amount', $obj['client_indv_soi_pension']['amount'], INTEGER, true);
				$Source = $this->validateParameter('Source', $obj['client_indv_soi_pension']['source'], STRING, true);
				
			}else if ($Category =="SELF-EMPLOYED"){
				
		
				$Barangay = $this->validateParameter('Barangay', $obj['client_indv_soi_selfemployed']['barangay'], STRING, true);
				$BldgNumber = $this->validateParameter('BldgNumber', $obj['client_indv_soi_selfemployed']['bldgNumber'], STRING, true);
				$City = $this->validateParameter('City', $obj['client_indv_soi_selfemployed']['city'], STRING, true);
				$ContactNumber = $this->validateParameter('ContactNumber', $obj['client_indv_soi_selfemployed']['contactNumber'], STRING, true);
				$ContactPerson = $this->validateParameter('ContactPerson', $obj['client_indv_soi_selfemployed']['contactPerson'], STRING, true);
				$NetIncome = $this->validateParameter('NetIncome', $obj['client_indv_soi_selfemployed']['netIncome'], INTEGER, true);
				$Name = $this->validateParameter('Name', $obj['client_indv_soi_selfemployed']['name'], STRING, true);
				$Nature = $this->validateParameter('Nature', $obj['client_indv_soi_selfemployed']['nature'], STRING, true);
				$Occupation = $this->validateParameter('Occupation', $obj['client_indv_soi_selfemployed']['occupation'], STRING, true);
				$Province = $this->validateParameter('Province', $obj['client_indv_soi_selfemployed']['province'], STRING, true);
				$Street = $this->validateParameter('Street', $obj['client_indv_soi_selfemployed']['street'], STRING, true);
				$YearsOfOperation = $this->validateParameter('YearsOfOperation', $obj['client_indv_soi_selfemployed']['yearsOfOperation'], STRING, true);
			
			
				
			}
			
			}
			
			//mc_loan_attachment
			$mc_loan_attachment = $this->mc_loan_attachment;
			foreach($mc_loan_attachment as $obj){
				
			$DocType = $this->validateParameter('DocType', $obj['docType'], STRING, true);
			$ImageBase64 = $this->validateParameter('ImageBase64', $obj['imageBase64'], STRING, FALSE);
			$Remarks = $this->validateParameter('Remarks', $obj['remarks'], STRING, true);
			
			}
			
			$this->Create_Application();
			
		}

		public function Create_Application() {
			
			//client information
			$FirstName = $this->validateParameter('FirstName', $this->client_personal_info['firstName'], STRING);
			$MiddleName = $this->validateParameter('MiddleName', $this->client_personal_info['middleName'], STRING, FALSE);
			$LastName = $this->validateParameter('LastName', $this->client_personal_info['lastName'], STRING);
			$SuffixName = $this->validateParameter('SuffixName', $this->client_personal_info['suffixName'], STRING, FALSE);
			$DOB = $this->validateParameter('DOB', $this->client_personal_info['dob'], STRING);
			$Gender = $this->validateParameter('Gender', $this->client_personal_info['gender'], STRING);
			$BirthPlace = $this->validateParameter('BirthPlace', $this->client_personal_info['birthPlace'], STRING);
			$Nationality = $this->validateParameter('Nationality', $this->client_personal_info['nationality'], STRING);
			$CivilStatus = $this->validateParameter('CivilStatus', $this->client_personal_info['civilStatus'], STRING);
			$MobileNumber = $this->validateParameter('MobileNumber', $this->client_personal_info['mobileNumber'], STRING);
			$PhoneNumber = $this->validateParameter('PhoneNumber', $this->client_personal_info['phoneNumber'], STRING , false);
			$Email = $this->validateParameter('Email', $this->client_personal_info['email'], STRING);
			$EducationalAttainment = $this->validateParameter('EducationalAttainment', $this->client_personal_info['educationalAttainment'], STRING);
			$Beneficiary = $this->validateParameter('Beneficiary', $this->client_personal_info['beneficiary'], STRING);
			$Age = $this->validateParameter('Age', $this->client_personal_info['age'], STRING, false);
			
			$client_indv_personal_info = new client_indv_personal_info;
			$client_indv_personal_info->setFirstName($FirstName);
			$client_indv_personal_info->setMiddleName($MiddleName);
			$client_indv_personal_info->setLastName($LastName);
			$client_indv_personal_info->setSuffixName($SuffixName);
			$client_indv_personal_info->setDOB($DOB);
			$client_indv_personal_info->setGender($Gender);
			$client_indv_personal_info->setBirthPlace($BirthPlace);
			$client_indv_personal_info->setNationality($Nationality);
			$client_indv_personal_info->setCivilStatus($CivilStatus);
			$client_indv_personal_info->setMobileNumber($MobileNumber);
			$client_indv_personal_info->setPhoneNumber($PhoneNumber);
			$client_indv_personal_info->setEmail($Email);
			$client_indv_personal_info->setEducationalAttainment($EducationalAttainment);
			$client_indv_personal_info->setBeneficiary($Beneficiary);
			$client_indv_personal_info->setCreated(date('Y-m-d H:i:s'));
			$client_indv_personal_info->setCreator($this->userId);
			
			$ClientId = $this->validateParameter('ClientId', $client_indv_personal_info->client_indv_personal_info(), INTEGER);
			$this->getClientID($ClientId);
			
			$this->mc_application_score($ClientId,"CIVIL STATUS",$CivilStatus);
			$this->mc_application_score($ClientId,"AGE",$Age);
			
			
			if($CivilStatus== "SINGLE"){
				
			}else{
				
				$this->Create_client_indv_spouse($ClientId);
				
			}
	
			
			$this->Create_Mc_loan_application($ClientId);
			$this->Create_Mc_loan_attachment($ClientId);
			$this->Create_client_address_current($ClientId);
			$this->Create_client_indv_address_permanent($ClientId);
			$this->Create_client_family_expenditure($ClientId);
			$this->Create_client_character_reference($ClientId);
			$this->Create_client_household_details($ClientId);
			$this->Create_client_identification($ClientId);
			$this->Create_client_indv_source_of_income($ClientId);
		
		  }
		  
		  public function mc_application_score($ClientId,$Type,$ScoreValue) {
			
			$mc_application_score = new mc_application_score;
			$mc_application_score->setClientInfoId($ClientId);
			$mc_application_score->setScoreType($Type);
			$mc_application_score->setScoreValue($ScoreValue);
			
			if(!$mc_application_score->generate_mc_application_score()){
							
			}else{
				
			}
		
		  }
		  
		  public function Create_client_indv_spouse($ClientId) {
			
			//client spouse information
			$FirstName = $this->validateParameter('FirstName', $this->client_indv_spouse['firstName'], STRING);
			$MiddleName = $this->validateParameter('MiddleName', $this->client_indv_spouse['middleName'], STRING, FALSE);
			$LastName = $this->validateParameter('LastName', $this->client_indv_spouse['lastName'], STRING);
			$SuffixName = $this->validateParameter('SuffixName', $this->client_indv_spouse['suffixName'], STRING, FALSE);
			$DOB = $this->validateParameter('DOB', $this->client_indv_spouse['dob'], STRING);
			$MobileNumber = $this->validateParameter('MobileNumber', $this->client_indv_spouse['mobileNumber'], STRING);
			//$PhoneNumber = $this->validateParameter('PhoneNumber', $this->client_indv_spouse['phoneNumber'], STRING, FALSE);
			$Email = $this->validateParameter('Email', $this->client_indv_spouse['email'], STRING, FALSE);
			
			$client_indv_spouse = new client_indv_spouse;
			$client_indv_spouse->setFirstName($FirstName);
			$client_indv_spouse->setMiddleName($MiddleName);
			$client_indv_spouse->setLastName($LastName);
			$client_indv_spouse->setSuffixName($SuffixName);
			$client_indv_spouse->setDOB($DOB);
			$client_indv_spouse->setMobileNumber($MobileNumber);
			//$client_indv_spouse->setPhoneNumber($PhoneNumber);
			$client_indv_spouse->setEmail($Email);
			$client_indv_spouse->setClientId($ClientId);
			$client_indv_spouse->setCreated(date('Y-m-d H:i:s'));
			$client_indv_spouse->setCreator($this->userId);
			
			if(!$client_indv_spouse->client_indv_spouse()){
				
			}else{
				
			}
		
		  }
		  
		public function Create_Mc_loan_application($ClientId) {
				  
			$this->data = json_decode($this->request, true);
			  
			
			//$ApplicationId = $this->validateParameter('ApplicationId',  $this->data['loanApplicationId'], INTEGER);
			$ApplicationDate = $this->validateParameter('ApplicationDate',  $this->data['applicationDate'], STRING);
			$Brand = $this->validateParameter('Brand',  $this->data['brand'], STRING);
			$Channel = $this->validateParameter('Channel',  $this->data['channel'], STRING);
			$Classification = $this->validateParameter('Classification',  $this->data['classification'], STRING);
			$Crsc = $this->validateParameter('Crsc',  $this->data['crsc'], STRING, FALSE);
			$DateTimeOnBoardStarted = $this->validateParameter('DateTimeOnBoardStarted',  $this->data['dateTimeOnBoardStarted'], STRING);
			$DealerCompany = $this->validateParameter('DealerCompany',  $this->data['dealerCompany'], STRING);
			$DealerKey = $this->validateParameter('DealerKey',  $this->data['dealerKey'], STRING);
			$DownPayment = $this->validateParameter('DownPayment',  $this->data['downPayment'], INTEGER);
			$FinancingBranch = $this->validateParameter('FinancingBranch',  $this->data['financingBranch'], STRING);
			$IsRepeatBorrower = $this->validateParameter('IsRepeatBorrower',  $this->data['isRepeatBorrower'], BOOLEAN,FALSE);
			$LoanType = $this->validateParameter('LoanType',  $this->data['loanType'], STRING);
			$Purpose = $this->validateParameter('Purpose',  $this->data['purpose'], STRING);
			$Rebate = $this->validateParameter('Rebate',  $this->data['rebate'], INTEGER);
			$MonthlyAmort = $this->validateParameter('MonthlyAmort',  $this->data['monthlyAmort'], INTEGER);
			$Term = $this->validateParameter('Term',  $this->data['term'], INTEGER);
			$SellingPrice = $this->validateParameter('SellingPrice',  $this->data['sellingPrice'], INTEGER);
			$Model = $this->validateParameter('Model',  $this->data['model'], STRING);
			$NumberOfUnit = $this->validateParameter('NumberOfUnit',  $this->data['numberOfUnit'], INTEGER);
			$DealerBranch = $this->validateParameter('DealerBranch',  $this->data['dealerBranchCode'], STRING);
			
			$Mc_loan_application = new Mc_loan_application;
					
			$Mc_loan_application->setClientInfoId($ClientId);
			$appID = $Mc_loan_application->generate_Mc_loan_application($ClientId);
			$Mc_loan_application->setApplicationId($appID['ApplicationId']);
			//$Mc_loan_application->setApplicationId($ApplicationId);
			$Mc_loan_application->setBrand($Brand);
			$Mc_loan_application->setChannel($Channel);
			$Mc_loan_application->setClassification($Classification);
			$Mc_loan_application->setApplicationDate($ApplicationDate);
			$Mc_loan_application->setCrsc($Crsc);
			$Mc_loan_application->setDateTimeOnBoardStarted($DateTimeOnBoardStarted);
			$Mc_loan_application->setDealerCompany($DealerCompany);
			$Mc_loan_application->setDealerKey($DealerKey);
			$Mc_loan_application->setDownPayment($DownPayment);
			$Mc_loan_application->setFinancingBranch($FinancingBranch);
			$Mc_loan_application->setIsRepeatBorrower($IsRepeatBorrower);
			$Mc_loan_application->setLoanType($LoanType);
			$Mc_loan_application->setPurpose($Purpose);
			$Mc_loan_application->setRebate($Rebate);
			$Mc_loan_application->setMonthlyAmort($MonthlyAmort);
			$Mc_loan_application->setTerm($Term);
			$Mc_loan_application->setSellingPrice($SellingPrice);
			$Mc_loan_application->setModel($Model);
			$Mc_loan_application->setNumberOfUnit($NumberOfUnit);
			$Mc_loan_application->setDealerBranch($DealerBranch);
			$Mc_loan_application->setStatus("NEW");
			$Mc_loan_application->setCreated(date('Y-m-d H:i:s'));
			$Mc_loan_application->setCreator($this->userId);
			
			
			if(!$Mc_loan_application->Mc_loan_application()){
				
			}else{
				
			}
			
			//$ClientId = $this->validateParameter('ClientId', $client_indv_personal_info->client_indv_personal_info(), INTEGER);
			 }
		  
		 
		
		public function Create_client_address_current($ClientId) {
			  
			$Barangay = $this->validateParameter('Barangay', $this->client_address_current['barangay'], STRING);
			$BlkNumber = $this->validateParameter('BlkNumber', $this->client_address_current['blkNumber'], STRING);
			$City = $this->validateParameter('City', $this->client_address_current['city'], STRING);
			$Duration = $this->validateParameter('Duration', $this->client_address_current['duration'], STRING);
			$Province = $this->validateParameter('Province', $this->client_address_current['province'], STRING);
			$Street = $this->validateParameter('Street', $this->client_address_current['street'], STRING);
			$Types = $this->validateParameter('Type', $this->client_address_current['type'], STRING);
			
			$client_indv_address_current = new client_indv_address_current;
			$client_indv_address_current->setClientId($ClientId);
			$client_indv_address_current->setBarangay($Barangay);
			$client_indv_address_current->setBlkNumber($BlkNumber);
			$client_indv_address_current->setCity($City);
			$client_indv_address_current->setDuration($Duration);
			$client_indv_address_current->setProvince($Province);
			$client_indv_address_current->setStreet($Street);
			$client_indv_address_current->setTypes($Types);
			$client_indv_address_current->setCreated(date('Y-m-d H:i:s'));
			$client_indv_address_current->setCreator($this->userId);
			
			// $this->$client_indv_address_current->client_indv_address_current();
			
			if(!$client_indv_address_current->client_indv_address_current()){
				
			}else{
				
			}
			 
		  }
		
		
		public function Create_client_indv_address_permanent($ClientId) {

			$Barangay = $this->validateParameter('Barangay', $this->client_address_permanent['barangay'], STRING);
			$BlkNumber = $this->validateParameter('BlkNumber', $this->client_address_permanent['blkNumber'], STRING);
			$City = $this->validateParameter('City', $this->client_address_permanent['city'], STRING);
			$Duration = $this->validateParameter('Duration', $this->client_address_permanent['duration'], STRING);
			$Province = $this->validateParameter('Province', $this->client_address_permanent['province'], STRING);
			$Street = $this->validateParameter('Street', $this->client_address_permanent['street'], STRING);
			$Types = $this->validateParameter('Type', $this->client_address_permanent['type'], STRING);
			
			$client_indv_address_permanent = new client_indv_address_permanent;
			$client_indv_address_permanent->setClientId($ClientId);
			$client_indv_address_permanent->setBarangay($Barangay);
			$client_indv_address_permanent->setBlkNumber($BlkNumber);
			$client_indv_address_permanent->setCity($City);
			$client_indv_address_permanent->setDuration($Duration);
			$client_indv_address_permanent->setProvince($Province);
			$client_indv_address_permanent->setStreet($Street);
			$client_indv_address_permanent->setTypes($Types);
			$client_indv_address_permanent->setCreated(date('Y-m-d H:i:s'));
			$client_indv_address_permanent->setCreator($this->userId);
			
			// $this->$client_indv_address_current->client_indv_address_current();
			
			if(!$client_indv_address_permanent->client_indv_address_permanent()){
				
			}else{
				
			}
			 
		}
		
		public function Create_client_family_expenditure($ClientId) {
			

			$Food = $this->validateParameter('Food', $this->client_family_expenditure['food'], INTEGER);
			$EducAllowance = $this->validateParameter('EducAllowance', $this->client_family_expenditure['educAllowance'], INTEGER);
			$EducTuitionFeePublic = $this->validateParameter('EducTuitionFeePublic', $this->client_family_expenditure['educTuitionFeePublic'], INTEGER);
			$EducTuitionFeePrivate = $this->validateParameter('EducTuitionFeePrivate', $this->client_family_expenditure['educTuitionFeePrivate'], INTEGER);
			$Electricity = $this->validateParameter('Electricity', $this->client_family_expenditure['electricity'], INTEGER);
			$Water = $this->validateParameter('Water', $this->client_family_expenditure['water'], INTEGER);
			$ELoad = $this->validateParameter('ELoad', $this->client_family_expenditure['eLoad'], INTEGER);
			$CableTV = $this->validateParameter('CableTV', $this->client_family_expenditure['cableTV'], INTEGER);
			$Internet = $this->validateParameter('Internet', $this->client_family_expenditure['internet'], INTEGER);
			$Transportation = $this->validateParameter('Transportation', $this->client_family_expenditure['transportation'], INTEGER);
			$Medical = $this->validateParameter('Medical', $this->client_family_expenditure['medical'], INTEGER);
			$ExistingObligation = $this->validateParameter('ExistingObligation', $this->client_family_expenditure['existingObligation'], INTEGER);
			$HouseRent = $this->validateParameter('HouseRent', $this->client_family_expenditure['houseRent'], INTEGER);
			$Utilities = $this->validateParameter('Utilities', $this->client_family_expenditure['utilities'], INTEGER);
			$Others = $this->validateParameter('Others', $this->client_family_expenditure['others'], INTEGER);
			$Miscellaneous = $this->validateParameter('Miscellaneous', $this->client_family_expenditure['miscellaneous'], INTEGER);
			
			$client_indv_family_expenditure = new client_indv_family_expenditure;
			$client_indv_family_expenditure->setClientId($ClientId);
			$client_indv_family_expenditure->setFood($Food);
			$client_indv_family_expenditure->setEducAllowance($EducAllowance);
			$client_indv_family_expenditure->setEducTuitionFeePublic($EducTuitionFeePublic);
			$client_indv_family_expenditure->setEducTuitionFeePrivate($EducTuitionFeePrivate);
			$client_indv_family_expenditure->setElectricity($Electricity);
			$client_indv_family_expenditure->setWater($Water);
			$client_indv_family_expenditure->setELoad($ELoad);
			$client_indv_family_expenditure->setCableTV($CableTV);
			$client_indv_family_expenditure->setMiscellaneous($Miscellaneous);
			$client_indv_family_expenditure->setInternet($Internet);
			$client_indv_family_expenditure->setTransportation($Transportation);
			$client_indv_family_expenditure->setMedical($Medical);
			$client_indv_family_expenditure->setExistingObligation($ExistingObligation);
			$client_indv_family_expenditure->setHouseRent($HouseRent);
			$client_indv_family_expenditure->setUtilities($Utilities);
			$client_indv_family_expenditure->setOthers($Others);
			$client_indv_family_expenditure->setCreated(date('Y-m-d H:i:s'));
			$client_indv_family_expenditure->setCreator($this->userId);
			
			// $this->$client_indv_address_current->client_indv_address_current();
			
			if(!$client_indv_family_expenditure->client_indv_family_expenditure()){
				
			}else{
				
			}
			 
		}
		
		public function Create_client_character_reference($ClientId) {
			
			
			//$data = json_decode($this->request, true);
			$data = $this->client_character_reference;
			
			foreach($data as $obj){
				
			$FullName = $this->validateParameter('FullName', $obj['fullName'], STRING, true);
			$Relationship = $this->validateParameter('Relationship', $obj['relationship'], STRING, true);
			$ContactNumber = $this->validateParameter('ContactNumber', $obj['contactNumber'], INTEGER, true);
			
			$client_indv_character_reference = new client_indv_character_reference;
			$client_indv_character_reference->setCreated(date('Y-m-d H:i:s'));
			$client_indv_character_reference->setCreator($this->userId);
			$client_indv_character_reference->setClientId($ClientId);
			$client_indv_character_reference->setFullName($FullName);
			$client_indv_character_reference->setRelationship($Relationship);
			$client_indv_character_reference->setContactNumber($ContactNumber);
			
			
			if(!$client_indv_character_reference->client_indv_character_reference()) {
				
				 // $message = 'Failed to insert.';
			} else {
							
				// $message = 'Inserted successful.';
				  
				// $message = $lvm->insertLV();
			}
			
		  }
			  // $this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function Create_client_household_details ($ClientId) {
			
			
			//$data = json_decode($this->request, true);
			$data = $this->client_household_details;
			
			foreach($data as $obj){
				
			$FullName = $this->validateParameter('FullName', $obj['fullName'], STRING, true);
			$Relationship = $this->validateParameter('Relationship', $obj['relationship'], STRING, true);
			$Age = $this->validateParameter('Age', $obj['age'], INTEGER, true);
			
			$client_indv_household_details = new client_indv_household_details;
			$client_indv_household_details->setCreated(date('Y-m-d H:i:s'));
			$client_indv_household_details->setCreator($this->userId);
			$client_indv_household_details->setClientId($ClientId);
			$client_indv_household_details->setFullName($FullName);
			$client_indv_household_details->setRelationship($Relationship);
			$client_indv_household_details->setAge($Age);
			
			
			if(!$client_indv_household_details->client_indv_household_details()) {
				
				 // $message = 'Failed to insert.';
			} else {
							
				// $message = 'Inserted successful.';
				  
				// $message = $lvm->insertLV();
			}
			
		  }
			  // $this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function Create_client_identification ($ClientId) {
			
			
			//$data = json_decode($this->request, true);
			$data = $this->client_identification;
			
			foreach($data as $obj){
				
			$IdNumber = $this->validateParameter('IdNumber', $obj['idNumber'], STRING, true);
			$Type = $this->validateParameter('Type', $obj['type'], STRING, true);
			
			
			$client_indv_identification = new client_indv_identification;
			$client_indv_identification->setCreated(date('Y-m-d H:i:s'));
			$client_indv_identification->setCreator($this->userId);
			$client_indv_identification->setClientId($ClientId);
			$client_indv_identification->setType($Type);
			$client_indv_identification->setIdNumber($IdNumber);
			
			
			if(!$client_indv_identification->client_indv_identification()) {
				
				 // $message = 'Failed to insert.';
			} else {
							
				// $message = 'Inserted successful.';
				  
				// $message = $lvm->insertLV();
			}
			
		  }
			  // $this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function Create_client_indv_source_of_income ($ClientId) {
			
			
			$data = $this->client_indv_source_of_income;
			
			foreach($data as $obj){
				
			$Category = $this->validateParameter('Category', $obj['category'], STRING, true);
			$Type = $this->validateParameter('Type', $obj['type'], STRING, true);
			
			
			$client_indv_source_of_income = new client_indv_source_of_income;
			$client_indv_source_of_income->setCreated(date('Y-m-d H:i:s'));
			$client_indv_source_of_income->setCreator($this->userId);
			$client_indv_source_of_income->setClientId($ClientId);
			$client_indv_source_of_income->setType($Type);
			$client_indv_source_of_income->setCategory($Category);
			
			$ParentId = $this->validateParameter('ParentId', $client_indv_source_of_income->client_indv_source_of_income(), INTEGER);
			
		
			if($Category == "PRIVATE EMPLOYEE"){
				
				$this->Create_client_indv_soi_private_employee ($ParentId,$obj['client_indv_soi_private_employee']);
				
			}else if ($Category =="GOVERNMENT EMPLOYEE"){
				
				$this->Create_client_indv_soi_goverment_employee ($ParentId,$obj['client_indv_soi_goverment_employee']);
				
				
			}else if ($Category =="BUSINESS"){
				
				 $this->Create_client_indv_soi_business ($ParentId,$obj['client_indv_soi_business']);
				
			}else if ($Category =="REMITTANCE"){
				
				$this->Create_client_indv_soi_remittance ($ParentId,$obj['client_indv_soi_remittance']);
				
				
			}else if ($Category =="PENSION"){
				
				$this->Create_client_indv_soi_pension ($ParentId,$obj['client_indv_soi_pension']);
				
				
			}else if ($Category =="SELF-EMPLOYED"){
				
				$this->Create_client_indv_soi_selfemployed ($ParentId,$obj['client_indv_soi_selfemployed']);
				
			}
			
		  }
			
		}
		
		public function Create_client_indv_soi_pension ($ParentId,$data) {
			
					
			$Amount = $this->validateParameter('Amount', $data['amount'], INTEGER, true);
			$Source = $this->validateParameter('Source', $data['source'], STRING, true);
			
			$client_indv_soi_pension = new client_indv_soi_pension;
			$client_indv_soi_pension->setCreated(date('Y-m-d H:i:s'));
			$client_indv_soi_pension->setCreator($this->userId);
			$client_indv_soi_pension->setParentId($ParentId);
			$client_indv_soi_pension->setSource($Source);
			$client_indv_soi_pension->setAmount($Amount);
			
			
			if(!$client_indv_soi_pension->client_indv_soi_pension()) {
				
				
			} else {
							
				
			}
				 
		}
		
		public function Create_client_indv_soi_remittance ($ParentId,$data) {
			
					
			$Amount = $this->validateParameter('Amount', $data['amount'], INTEGER, true);
			$Sender = $this->validateParameter('Sender', $data['sender'], STRING, true);
			$Country = $this->validateParameter('Country', $data['country'], STRING, true);
			$Relationship = $this->validateParameter('Relationship', $data['relationship'], STRING, true);
			
			$client_indv_soi_remittance = new client_indv_soi_remittance;
			$client_indv_soi_remittance->setCreated(date('Y-m-d H:i:s'));
			$client_indv_soi_remittance->setCreator($this->userId);
			$client_indv_soi_remittance->setParentId($ParentId);
			$client_indv_soi_remittance->setSender($Sender);
			$client_indv_soi_remittance->setAmount($Amount);
			$client_indv_soi_remittance->setCountry($Country);
			$client_indv_soi_remittance->setRelationship($Relationship);
			
			
			if(!$client_indv_soi_remittance->client_indv_soi_remittance()) {
				
				
			} else {
							
				
			}
				 
		}
		
		public function Create_client_indv_soi_goverment_employee ($ParentId,$data) {
					
			$Barangay = $this->validateParameter('Barangay', $data['barangay'], STRING, true);
			$BldgNumber = $this->validateParameter('BldgNumber', $data['bldgNumber'], STRING, true);
			$City = $this->validateParameter('City', $data['city'], STRING, true);
			$CompanyName = $this->validateParameter('CompanyName', $data['companyName'], STRING, true);
			$ContactNumber = $this->validateParameter('ContactNumber', $data['contactNumber'], STRING, true);
			$ContactPerson = $this->validateParameter('ContactPerson', $data['contactPerson'], STRING, true);
			$EmploymentStatus = $this->validateParameter('EmploymentStatus', $data['employmentStatus'], STRING, true);
			$NatureOfWork = $this->validateParameter('NatureOfWork', $data['natureOfWork'], STRING, true);
			$NetIncome = $this->validateParameter('NetIncome', $data['netIncome'], INTEGER, true);
			$Occupation = $this->validateParameter('Occupation', $data['occupation'], STRING, true);
			$Position = $this->validateParameter('Position', $data['position'], STRING, true);
			$Province = $this->validateParameter('Province', $data['province'], STRING, true);
			$Street = $this->validateParameter('Street', $data['street'], STRING, true);
			$Tenure = $this->validateParameter('Tenure', $data['tenure'], STRING, true);
			
			$client_indv_soi_goverment_employee = new client_indv_soi_goverment_employee;
			$client_indv_soi_goverment_employee->setCreated(date('Y-m-d H:i:s'));
			$client_indv_soi_goverment_employee->setCreator($this->userId);
			$client_indv_soi_goverment_employee->setParentId($ParentId);
			$client_indv_soi_goverment_employee->setBarangay($Barangay);
			$client_indv_soi_goverment_employee->setBldgNumber($BldgNumber);
			$client_indv_soi_goverment_employee->setCity($City);
			$client_indv_soi_goverment_employee->setCompanyName($CompanyName);
			$client_indv_soi_goverment_employee->setContactNumber($ContactNumber);
			$client_indv_soi_goverment_employee->setContactPerson($ContactPerson);
			$client_indv_soi_goverment_employee->setEmploymentStatus($EmploymentStatus);
			$client_indv_soi_goverment_employee->setNatureOfWork($NatureOfWork);
			$client_indv_soi_goverment_employee->setNetIncome($NetIncome);
			$client_indv_soi_goverment_employee->setOccupation($Occupation);
			$client_indv_soi_goverment_employee->setPosition($Position);
			$client_indv_soi_goverment_employee->setProvince($Province);
			$client_indv_soi_goverment_employee->setStreet($Street);
			$client_indv_soi_goverment_employee->setTenure($Tenure);
			
			
			if(!$client_indv_soi_goverment_employee->client_indv_soi_goverment_employee()) {
				
				
			} else {
							
				
			}
				 
		}
		
		public function Create_client_indv_soi_private_employee ($ParentId,$data) {
					
			$Barangay = $this->validateParameter('Barangay', $data['barangay'], STRING, true);
			$BldgNumber = $this->validateParameter('BldgNumber', $data['bldgNumber'], STRING, true);
			$City = $this->validateParameter('City', $data['city'], STRING, true);
			$CompanyName = $this->validateParameter('CompanyName', $data['companyName'], STRING, true);
			$ContactNumber = $this->validateParameter('ContactNumber', $data['contactNumber'], STRING, true);
			$ContactPerson = $this->validateParameter('ContactPerson', $data['contactPerson'], STRING, true);
			$EmploymentStatus = $this->validateParameter('EmploymentStatus', $data['employmentStatus'], STRING, true);
			$NatureOfWork = $this->validateParameter('NatureOfWork', $data['natureOfWork'], STRING, true);
			$NetIncome = $this->validateParameter('NetIncome', $data['netIncome'], INTEGER, true);
			$Occupation = $this->validateParameter('Occupation', $data['occupation'], STRING, true);
			$Position = $this->validateParameter('Position', $data['position'], STRING, true);
			$Province = $this->validateParameter('Province', $data['province'], STRING, true);
			$Street = $this->validateParameter('Street', $data['street'], STRING, true);
			$Tenure = $this->validateParameter('Tenure', $data['tenure'], STRING, true);
			
			$client_indv_soi_private_employee = new client_indv_soi_private_employee;
			$client_indv_soi_private_employee->setCreated(date('Y-m-d H:i:s'));
			$client_indv_soi_private_employee->setCreator($this->userId);
			$client_indv_soi_private_employee->setParentId($ParentId);
			$client_indv_soi_private_employee->setBarangay($Barangay);
			$client_indv_soi_private_employee->setBldgNumber($BldgNumber);
			$client_indv_soi_private_employee->setCity($City);
			$client_indv_soi_private_employee->setCompanyName($CompanyName);
			$client_indv_soi_private_employee->setContactNumber($ContactNumber);
			$client_indv_soi_private_employee->setContactPerson($ContactPerson);
			$client_indv_soi_private_employee->setEmploymentStatus($EmploymentStatus);
			$client_indv_soi_private_employee->setNatureOfWork($NatureOfWork);
			$client_indv_soi_private_employee->setNetIncome($NetIncome);
			$client_indv_soi_private_employee->setOccupation($Occupation);
			$client_indv_soi_private_employee->setPosition($Position);
			$client_indv_soi_private_employee->setProvince($Province);
			$client_indv_soi_private_employee->setStreet($Street);
			$client_indv_soi_private_employee->setTenure($Tenure);
			
			
			if(!$client_indv_soi_private_employee->client_indv_soi_private_employee()) {
				
				
			} else {
							
				
			}
				 
		}
		  
		public function Create_client_indv_soi_business ($ParentId,$data) {
					
			$Barangay = $this->validateParameter('Barangay', $data['barangay'], STRING, true);
			$BldgNumber = $this->validateParameter('BldgNumber', $data['bldgNumber'], STRING, true);
			$City = $this->validateParameter('City', $data['city'], STRING, true);
			$ContactNumber = $this->validateParameter('ContactNumber', $data['contactNumber'], STRING, true);
			$ContactPerson = $this->validateParameter('ContactPerson', $data['contactPerson'], STRING, true);
			$NetIncome = $this->validateParameter('NetIncome', $data['netIncome'], INTEGER, true);
			$Name = $this->validateParameter('Name', $data['name'], STRING, true);
			$Nature = $this->validateParameter('Nature', $data['nature'], STRING, true);
			$Occupation = $this->validateParameter('Occupation', $data['occupation'], STRING, true);
			$Province = $this->validateParameter('Province', $data['province'], STRING, true);
			$Street = $this->validateParameter('Street', $data['street'], STRING, true);
			$YearsOfOperation = $this->validateParameter('YearsOfOperation', $data['yearsOfOperation'], STRING, true);
			
			
			$client_indv_soi_business = new client_indv_soi_business;
			$client_indv_soi_business->setCreated(date('Y-m-d H:i:s'));
			$client_indv_soi_business->setCreator($this->userId);
			$client_indv_soi_business->setParentId($ParentId);
			$client_indv_soi_business->setBarangay($Barangay);
			$client_indv_soi_business->setBldgNumber($BldgNumber);
			$client_indv_soi_business->setCity($City);
			$client_indv_soi_business->setName($Name);
			$client_indv_soi_business->setContactNumber($ContactNumber);
			$client_indv_soi_business->setContactPerson($ContactPerson);
			$client_indv_soi_business->setNature($Nature);
			$client_indv_soi_business->setNetIncome($NetIncome);
			$client_indv_soi_business->setOccupation($Occupation);
			$client_indv_soi_business->setProvince($Province);
			$client_indv_soi_business->setStreet($Street);
			$client_indv_soi_business->setYearsOfOperation($YearsOfOperation);
			
			
			if(!$client_indv_soi_business->client_indv_soi_business()) {
				
				
			} else {
							
				
			}
				 
		}
		
		
		public function Create_client_indv_soi_selfemployed ($ParentId,$data) {
					
			$Barangay = $this->validateParameter('Barangay', $data['barangay'], STRING, true);
			$BldgNumber = $this->validateParameter('BldgNumber', $data['bldgNumber'], STRING, true);
			$City = $this->validateParameter('City', $data['city'], STRING, true);
			$ContactNumber = $this->validateParameter('ContactNumber', $data['contactNumber'], STRING, true);
			$ContactPerson = $this->validateParameter('ContactPerson', $data['contactPerson'], STRING, true);
			$NetIncome = $this->validateParameter('NetIncome', $data['netIncome'], INTEGER, true);
			$Name = $this->validateParameter('Name', $data['name'], STRING, true);
			$Nature = $this->validateParameter('Nature', $data['nature'], STRING, true);
			$Occupation = $this->validateParameter('Occupation', $data['occupation'], STRING, true);
			$Province = $this->validateParameter('Province', $data['province'], STRING, true);
			$Street = $this->validateParameter('Street', $data['street'], STRING, true);
			$YearsOfOperation = $this->validateParameter('YearsOfOperation', $data['yearsOfOperation'], STRING, true);
			
			
			$client_indv_soi_selfemployed = new client_indv_soi_selfemployed;
			$client_indv_soi_selfemployed->setCreated(date('Y-m-d H:i:s'));
			$client_indv_soi_selfemployed->setCreator($this->userId);
			$client_indv_soi_selfemployed->setParentId($ParentId);
			$client_indv_soi_selfemployed->setBarangay($Barangay);
			$client_indv_soi_selfemployed->setBldgNumber($BldgNumber);
			$client_indv_soi_selfemployed->setCity($City);
			$client_indv_soi_selfemployed->setName($Name);
			$client_indv_soi_selfemployed->setContactNumber($ContactNumber);
			$client_indv_soi_selfemployed->setContactPerson($ContactPerson);
			$client_indv_soi_selfemployed->setNature($Nature);
			$client_indv_soi_selfemployed->setNetIncome($NetIncome);
			$client_indv_soi_selfemployed->setOccupation($Occupation);
			$client_indv_soi_selfemployed->setProvince($Province);
			$client_indv_soi_selfemployed->setStreet($Street);
			$client_indv_soi_selfemployed->setYearsOfOperation($YearsOfOperation);
			
			
			if(!$client_indv_soi_selfemployed->client_indv_soi_selfemployed()) {
				
				
			} else {
							
				
			}
				 
		}
		
		public function Create_Mc_loan_attachment($ClientId) {
			
			
			//$data = json_decode($this->request, true);
			$data = $this->mc_loan_attachment;
			foreach($data as $obj){
				
			$DocType = $this->validateParameter('DocType', $obj['docType'], STRING, true);
			$ImageBase64 = $this->validateParameter('ImageBase64', $obj['imageBase64'], STRING, FALSE);
			$Remarks = $this->validateParameter('Remarks', $obj['remarks'], STRING, true);
			$Mc_loan_attachment = new Mc_loan_attachment;
			$Mc_loan_attachment->setCreated(date('Y-m-d H:i:s'));
			$Mc_loan_attachment->setCreator($this->userId);
			$Mc_loan_attachment->setParentId($ClientId);
			$Mc_loan_attachment->setDocType($DocType);
			$Mc_loan_attachment->setImageBase64($ImageBase64);
			$Mc_loan_attachment->setRemarks($Remarks);
			
			
			if(!$Mc_loan_attachment->Mc_loan_attachment()) {
				
				 // $message = 'Failed to insert.';
			} else {
							 // $this->returnResponse(SUCCESS_RESPONSE, $message);
				// $message = 'Inserted successful.';
				  
				// $message = $lvm->insertLV();
			}
			
		  }
			 
		}
	}
	
 ?>