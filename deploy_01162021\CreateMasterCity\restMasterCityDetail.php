<?php 
	include_once '../Lib/constants.php';
	include_once '../Lib/DbConnect.php';
	include_once '../Lib/jwt.php';
	
	class RestMasterCity{
		protected $request;
		protected $serviceName;
		protected $param;
		protected $dbConn;
		protected $userId;

		public function __construct() {
			
				$request_method = $_SERVER['REQUEST_METHOD'];
			
				$handler = fopen('php://input', 'r');
			
				$this->request = stream_get_contents( $handler);
		
							 $db = new DbConnect;
							 $this->dbConn = $db->connect();	
							 $this->validateToken();														 
						
		 }

		Public function validateRequest() {
			if($_SERVER['CONTENT_TYPE'] !== 'application/json') {
				$this->throwError(REQUEST_CONTENTTYPE_NOT_VALID, 'Request content type is not valid');
			}

		}

		public function validateParameter($fieldName, $value, $dataType, $required = true) {
			if($required == true && empty($value) == true) {
				$this->throwError(VALIDATE_PARAMETER_REQUIRED, $fieldName . " parameter is required.");
			}

			switch ($dataType) {
				case BOOLEAN:
					if(!is_bool($value)) {
						$this->throwError(VALIDATE_PARAMETER_DATATYPE, "Datatype is not valid for " . $fieldName . '. It should be boolean.');
					}
					break;
				case INTEGER:
					if(!is_numeric($value)) {
						$this->throwError(VALIDATE_PARAMETER_DATATYPE, "Datatype is not valid for " . $fieldName . '. It should be numeric.');
					}
					break;

				case STRING:
					if(!is_string($value)) {
						$this->throwError(VALIDATE_PARAMETER_DATATYPE, "Datatype is not valid for " . $fieldName . '. It should be string.');
					}
					break;
				
				default:
					$this->throwError(VALIDATE_PARAMETER_DATATYPE, "Datatype is not valid for " . $fieldName);
					break;
			}

			return $value;

		}
		
		public function validateToken() {
			try {
				$token = $this->getBearerToken();
				$payload = JWT::decode($token, SECRETE_KEY, ['HS256']);
				
				$stmt = $this->dbConn->prepare("SELECT UserID,Company,Branch,LName,FName,MName,EmployeeName,Position,UserName,Password,
					DateCreated,GroupID,Flag,LogIn,datediff(day,getdate(),ExpiryDate)Diff,Locked,LastLogIn,LogInAttempt,
					Initialized,ip_add,host_name,mac_add,comp_dtl,cp_number,hash_uname 
				FROM tbluser WHERE UserID = :userId");
				$stmt->bindParam(":userId", $payload->userId);
				$stmt->execute();
				$user = $stmt->fetch(PDO::FETCH_ASSOC);
				if(!is_array($user)) {
					 http_response_code(404);
					$this->returnResponse(INVALID_USER_PASS, "This user is not found in our database.");
				}

				if($user['Diff'] < 1) {
					http_response_code(401);
					$this->throwError('Failed', "User account has been expired!");
				}else if($user['Locked'] == 1) {
					http_response_code(401);
					$this->throwError('Failed', "User Account has been locked!");
				}else if($user['Flag'] == 0) {
					http_response_code(401);
					$this->throwError('Failed', "User Account is pending for activation!");
				}else if($user['Flag'] == 2) {
					http_response_code(401);
					$this->throwError('Failed', "Registration denied!");
				}else if($user['Flag'] == 3) {
					http_response_code(401);
					$this->throwError('Failed', "User Account is deactivated!");
				}
				
				$this->userId = $payload->userId;
				
				 http_response_code(200);
				
			} catch (Exception $e) {
				http_response_code(401);
				$this->throwError(ACCESS_TOKEN_ERRORS, $e->getMessage());
			}
		}

		public function processMasterCityDetail() {
			
			if($_SERVER['REQUEST_METHOD'] == 'POST' ){
				
				$this->validateRequest();
				
					if(!isset($_GET['SysId']) || $_GET['SysId'] == "") {
						
							$this->addMasterCity();
								
						
					}else{
					
						$SysId = $this->validateParameter('SysId', $_GET['SysId'], INTEGER, false);
					
						$this->updateMasterCity($SysId);
					}
							 
				 
			}else if ($_SERVER['REQUEST_METHOD'] == 'GET' ){
				
				//$this->validateRequest();
				
				$this->getMasterCityDetails();

			}
						
		}

		public function throwError($code, $message) {
			header("content-type: application/json");
			$errorMsg = json_encode(['error' => ['status'=>$code, 'message'=>$message]]);
			echo $errorMsg; exit;
		}

		public function returnResponse($code, $data) {
			header("content-type: application/json");
			// $response = json_encode(['resonse' => ['status' => $code, "result" => $data]]);
			$response = json_encode( ['message' => $code, "data" => $data]);
			echo $response; exit;
		}
		
		public function returnResponseGetAll($data) {
			header("content-type: application/json");
			$response = json_encode($data);
			echo $response; exit;
		}
		
	    public function getAuthorizationHeader(){
	        $headers = null;
	        if (isset($_SERVER['Authorization'])) {
	            $headers = trim($_SERVER["Authorization"]);
	        }
	        else if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
	            $headers = trim($_SERVER["HTTP_AUTHORIZATION"]);
	        } elseif (function_exists('apache_request_headers')) {
	            $requestHeaders = apache_request_headers();
	            $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));
	            if (isset($requestHeaders['Authorization'])) {
	                $headers = trim($requestHeaders['Authorization']);
	            }
	        }
	        return $headers;
	    }
	
	    public function getBearerToken() {
	        $headers = $this->getAuthorizationHeader();
	        
	        if (!empty($headers)) {
	            if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
	                return $matches[1];
	            }
	        }
	        $this->throwError( ATHORIZATION_HEADER_NOT_FOUND, 'Access Token Not found');
	    }
	}
 ?>