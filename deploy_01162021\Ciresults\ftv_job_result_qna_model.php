<?php 
include_once '../Lib/DbConnect.php';

	class ftv_job_result_qna_model {
		
		
		private $tableName = 'ftv_job_result_q_n_aa';
		private $dbConn;
			
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $JobId;
		private $Question;
		private $Answer;
		private $OrderNumber;
		private $Verified;
		private $Remarks;	
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
			
		function setOrderNumber($OrderNumber) { $this->OrderNumber = $OrderNumber; }
		function getOrderNumber() { return $this->OrderNumber; }
		
		function setVerified($Verified) { $this->Verified = $Verified; }
		function getVerified() { return $this->Verified; }
		
		function setRemarks($Remarks) { $this->Remarks = $Remarks; }
		function getRemarks() { return $this->Remarks; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setAnswer($Answer) { $this->Answer = $Answer; }
		function getAnswer() { return $this->Answer; }

		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setJobId($JobId) { $this->JobId = $JobId; }
		function getJobId() { return $this->JobId; }
				
		function setQuestion($Question) { $this->Question = $Question; }
		function getQuestion() { return $this->Question; }
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
		

				
		public function ftv_job_result_q_n_aa() {
			
			$sql = 'INSERT INTO  ftv_job_result_q_n_aa (Creator,Created,JobId,Question,Answer,OrderNumber,Verified,Remarks) 
								 VALUES(:Creator,:Created,:JobId,:Question,:Answer,:OrderNumber,:Verified,:Remarks)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':JobId', $this->JobId);
			$stmt->bindParam(':Question', $this->Question);
			$stmt->bindParam(':Answer', $this->Answer);
			$stmt->bindParam(':OrderNumber', $this->OrderNumber);
			$stmt->bindParam(':Verified', $this->Verified);
			$stmt->bindParam(':Remarks', $this->Remarks);
	
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>