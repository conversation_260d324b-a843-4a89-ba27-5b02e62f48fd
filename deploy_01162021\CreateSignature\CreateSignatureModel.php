<?php 
include_once '../Lib/DbConnect.php';

	class CreateSignatureModel {
		
		public $tableName = 'Mc_DocumentsSignature';
		public $dbConn;
		
		public $SysId;
		public $Created;
		public $Creator;
		public $LastModified;
		public $Modifier;
		public $ApplicationID;
		public $DocumentCode;
		public $ImageBase64;
		public $IsUsed;
	
		
		function setIsUsed($IsUsed) { $this->IsUsed = $IsUsed; }
		function getIsUsed() { return $this->IsUsed; }
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setApplicationID($ApplicationID) { $this->ApplicationID = $ApplicationID; }
		function getApplicationID() { return $this->ApplicationID; }
		
		
		function setDocumentCode($DocumentCode) { $this->DocumentCode = $DocumentCode; }
		function getDocumentCode() { return $this->DocumentCode; }
		
		function setImageBase64($ImageBase64) { $this->ImageBase64 = $ImageBase64; }
		function getImageBase64() { return $this->ImageBase64; }
		
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}


		public function insertSignature() {
			
			// $sql = "SELECT count(1) FROM Mc_DocumentsSignature WHERE ApplicationID";
			
					// $stmt = $this->dbConn->prepare($sql);
					// $stmt->bindParam(':ApplicationID', $this->ApplicationID);
					// $stmt->bindParam(':DocumentCode', $this->DocumentCode);
					// $stmt->execute();
					// $Signature = $stmt->fetchAll(PDO::FETCH_ASSOC);
					
			
			
		// $tr = sqlsrv_query($conn,"SELECT count(1) FROM Mc_DocumentsSignature WHERE ApplicationID = '".$fc."' and prod_id = '".$prod."'");
	
		// if ($tr == true) {
		// $row = sqlsrv_fetch_array($tr);
		
		// if($row[0] == 0)
		// {
		
					$sql = 'INSERT INTO  Mc_DocumentsSignature (Creator,Created,ApplicationID,DocumentCode,ImageBase64,IsUsed) 
												VALUES(:Creator,:Created,:ApplicationID,:DocumentCode,:ImageBase64,:IsUsed)';
			
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':Creator', $this->Creator);
					$stmt->bindParam(':Created', $this->Created);
					$stmt->bindParam(':ApplicationID', $this->ApplicationID);
					$stmt->bindParam(':DocumentCode', $this->DocumentCode);
					$stmt->bindParam(':ImageBase64', $this->ImageBase64);
					$stmt->bindParam(':IsUsed', $this->IsUsed);
					
					if($stmt->execute()) {				 
									 
						return true;
						
					} else {
						return false;
					}
	
			
		// } else 
		// {
			// echo 'ex:Dempographics already exists!';
		// }
	// } else 
	// {
		// echo 'no:Check Duplicates - Demographics'. print_r(sqlsrv_errors(),true);
	// }

		}
	}
 ?>