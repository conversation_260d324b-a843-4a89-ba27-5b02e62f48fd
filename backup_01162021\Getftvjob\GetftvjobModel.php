<?php 
include_once '../Lib/DbConnect.php';

	class GetftvjobModel {
		
		private $tableName = 'ftv_job';
		private $dbConn;
			
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $ApplicationID;
		private $BlkNumber;
		private $Street;
		private $Subdivision;
		private $Barangay;
		private $City;
		private $Province;
		private $Type;
		private $Category;
		private $Classification;
		private $DateTimeStarted;
		private $DateTimeCompleted;
		private $CreditInvestigator;
		private $Status;
		private $Submit_Lat;
		private $Submit_Lon;
		private $DoneFTV_Lat;
		private $DoneFTV_Lon;
		private $Remarks;
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setDoneFTV_Lat($DoneFTV_Lat) { $this->DoneFTV_Lat = $DoneFTV_Lat; }
		function getDoneFTV_Lat() { return $this->DoneFTV_Lat; }
		
		function setDoneFTV_Lon($DoneFTV_Lon) { $this->DoneFTV_Lon = $DoneFTV_Lon; }
		function getDoneFTV_Lon() { return $this->DoneFTV_Lon; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setApplicationID($ApplicationID) { $this->ApplicationID = $ApplicationID; }
		function getApplicationID() { return $this->ApplicationID; }
		
		function setBlkNumber($BlkNumber) { $this->BlkNumber = $BlkNumber; }
		function getBlkNumber() { return $this->BlkNumber; }
		
		function setStreet($Street) { $this->Street = $Street; }
		function getStreet() { return $this->Street; }
		
		function setSubdivision($Subdivision) { $this->Subdivision = $Subdivision; }
		function getSubdivision() { return $this->Subdivision; }
		
		function setBarangay($Barangay) { $this->Barangay = $Barangay; }
		function getBarangay() { return $this->Barangay; }
		
		function setCity($City) { $this->City = $City; }
		function getCity() { return $this->City; }
		
		function setProvince($Province) { $this->Province = $Province; }
		function getProvince() { return $this->Province; }
		
		function setType($Type) { $this->Type = $Type; }
		function getType() { return $this->Type; }
		
		function setCategory($Category) { $this->Category = $Category; }
		function getCategory() { return $this->Category; }
		
		function setClassification($Classification) { $this->Classification = $Classification; }
		function getClassification() { return $this->Classification; }
		
		function setDateTimeCompleted($DateTimeCompleted) { $this->DateTimeCompleted = $DateTimeCompleted; }
		function getDateTimeCompleted() { return $this->DateTimeCompleted; }
		
		function setDateTimeStarted($DateTimeStarted) { $this->DateTimeStarted = $DateTimeStarted; }
		function getDateTimeStarted() { return $this->DateTimeStarted; }
		
		function setCreditInvestigator($CreditInvestigator) { $this->CreditInvestigator = $CreditInvestigator; }
		function getCreditInvestigator() { return $this->CreditInvestigator; }
		
		function setStatus($Status) { $this->Status = $Status; }
		function getStatus() { return $this->Status; }
				
		function setSubmit_Lat($Submit_Lat) { $this->Submit_Lat = $Submit_Lat; }
		function getSubmit_Lat() { return $this->Submit_Lat; }
				
		function setSubmit_Lon($Submit_Lon) { $this->Submit_Lon = $Submit_Lon; }
		function getSubmit_Lon() { return $this->Submit_Lon; }
				
		function setRemarks($Remarks) { $this->Remarks = $Remarks; }
		function getRemarks() { return $this->Remarks; }
		
		
	
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		// public function Getftvjob() {
					
					// $sql = "select * from Getftvjob a where a.DealerBranchKey = :DealerBranchKey";
					// $stmt = $this->dbConn->prepare($sql);
					// $stmt->bindParam(':DealerBranchKey', $this->DealerBranchKey);
					// $stmt->execute();
					// $Getftvjob = $stmt->fetchAll(PDO::FETCH_ASSOC);
					// return $Getftvjob;
					
		// }
		
		public function getAllftvjob() {
			
			// select a.SysId,CONCAT(bs.FirstName,' ',bs.MiddleName,' ',bs.LastName,' ',bs.SuffixName) fullName,c.ImageBase64 applicantPhoto

			// from ftv_job a

			// inner join Mc_loan_application b on a.ApplicationID = b.ApplicationID

			// left join client_indv_personal_info bs on bs.SysId = b.ClientInfoId

			// left join (select ParentId,ImageBase64 from Mc_loan_attachment where DocType = 'PHOTO - CLIENT') c on c.ParentId = bs.SysId

			// where a.status = 'JOB CREATED'
					
					$sql = "select a.SysId sysId,a.Created created,a.Creator creator,a.LastModified lastModified,a.Modifier modifier,a.ApplicationID applicationID,
							 a.Category category,a.Classification classification,a.BlkNumber blkNumber,a.Street street,
							 a.Subdivision subdivision,a.Barangay barangay,a.City city,a.Province province,a.DateTimeCompleted dateTimeCompleted,
							 a.DateTimeStarted dateTimeStarted,a.CreditInvestigator creditInvestigator,
							 
							 a.Status status,a.Submit_Lat submit_Lat,a.Submit_Lon submit_Lon,a.DoneFTV_Lat doneFTV_Lat,a.DoneFTV_Lon doneFTV_Lon,a.Type type,a.Remarks remarks,
							 
							 CONCAT(bs.FirstName,' ',bs.MiddleName,' ',bs.LastName,' ',bs.SuffixName) fullName,c.ImageBase64 applicantPhoto

			 from ftv_job a

			 inner join Mc_loan_application b on a.ApplicationID = b.SysId

			 left join client_indv_personal_info bs on bs.SysId = b.ClientInfoId

			 left join (select ParentId,ImageBase64 from Mc_loan_attachment where DocType = 'PHOTO - CLIENT') c on c.ParentId = bs.SysId

			 where a.status not in ('JOB COMPLETED','JOB VERIFIED') and CreditInvestigator =:CreditInvestigator";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':CreditInvestigator', $this->CreditInvestigator);
					$stmt->execute();
					$Getftvjob = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $Getftvjob;
		}
	}
 ?>