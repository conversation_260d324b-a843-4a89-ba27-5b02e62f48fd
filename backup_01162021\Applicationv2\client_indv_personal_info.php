<?php 
include_once '../Lib/DbConnect.php';

	class client_indv_personal_info {
		
		
		private $tableName = 'client_indv_personal_info';
		private $dbConn;
			
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $FirstName;
		private $MiddleName;
		private $LastName;
		private $SuffixName;
		private $DOB;
		private $BirthPlace;
		private $Nationality;
		private $CivilStatus;
		private $Gender;
		private $MobileNumber;
		private $PhoneNumber;
		private $Email;
		private $EducationalAttainment;
		private $Beneficiary;
	
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setFirstName($FirstName) { $this->FirstName = $FirstName; }
		function getFirstName() { return $this->FirstName; }
		
		function setMiddleName($MiddleName) { $this->MiddleName = $MiddleName; }
		function getMiddleName() { return $this->MiddleName; }
		
		function setLastName($LastName) { $this->LastName = $LastName; }
		function getLastName() { return $this->LastName; }
		
		function setSuffixName($SuffixName) { $this->SuffixName = $SuffixName; }
		function getSuffixName() { return $this->SuffixName; }
		
		function setDOB($DOB) { $this->DOB = $DOB; }
		function getDOB() { return $this->DOB; }
		
		function setBirthPlace($BirthPlace) { $this->BirthPlace = $BirthPlace; }
		function getBirthPlace() { return $this->BirthPlace; }
		
		function setNationality($Nationality) { $this->Nationality = $Nationality; }
		function getNationality() { return $this->Nationality; }
		
		function setCivilStatus($CivilStatus) { $this->CivilStatus = $CivilStatus; }
		function getCivilStatus() { return $this->CivilStatus; }
		
		function setGender($Gender) { $this->Gender = $Gender; }
		function getGender() { return $this->Gender; }
		
		function setMobileNumber($MobileNumber) { $this->MobileNumber = $MobileNumber; }
		function getMobileNumber() { return $this->MobileNumber; }
		
		function setPhoneNumber($PhoneNumber) { $this->PhoneNumber = $PhoneNumber; }
		function getPhoneNumber() { return $this->PhoneNumber; }
		
		function setEmail($Email) { $this->Email = $Email; }
		function getEmail() { return $this->Email; }
		
		function setEducationalAttainment($EducationalAttainment) { $this->EducationalAttainment = $EducationalAttainment; }
		function getEducationalAttainment() { return $this->EducationalAttainment; }
		
		function setBeneficiary($Beneficiary) { $this->Beneficiary = $Beneficiary; }
		function getBeneficiary() { return $this->Beneficiary; }
		

		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
				
		public function client_indv_personal_info() {
			
			$sql = 'INSERT INTO  client_indv_personal_info (Creator,Created,FirstName,MiddleName,LastName,SuffixName,DOB,BirthPlace,Nationality,CivilStatus,Gender,MobileNumber,PhoneNumber,Email,EducationalAttainment,Beneficiary) 
													VALUES (:Creator,:Created,:FirstName,:MiddleName,:LastName,:SuffixName,:DOB,:BirthPlace,:Nationality,:CivilStatus,:Gender,:MobileNumber,:PhoneNumber,:Email,:EducationalAttainment,:Beneficiary)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':FirstName', $this->FirstName);
			$stmt->bindParam(':MiddleName', $this->MiddleName);
			$stmt->bindParam(':LastName', $this->LastName);
			$stmt->bindParam(':SuffixName', $this->SuffixName);
			$stmt->bindParam(':DOB', $this->DOB);
			$stmt->bindParam(':BirthPlace', $this->BirthPlace);
			$stmt->bindParam(':Nationality', $this->Nationality);
			$stmt->bindParam(':CivilStatus', $this->CivilStatus);
			$stmt->bindParam(':Gender', $this->Gender);
			$stmt->bindParam(':MobileNumber', $this->MobileNumber);
			$stmt->bindParam(':PhoneNumber', $this->PhoneNumber);
			$stmt->bindParam(':Email', $this->Email);
			$stmt->bindParam(':EducationalAttainment', $this->EducationalAttainment);
			$stmt->bindParam(':Beneficiary', $this->Beneficiary);
			
			if($stmt->execute()) {
				
				$SysId = $this->dbConn->lastInsertId();
				return $SysId;
			} else {
				$SysId = null;
				return $SysId;
			}
		}
	}
 ?>