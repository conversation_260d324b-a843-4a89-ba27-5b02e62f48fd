<?php 
	/**
	* Database Connection
	*/
	class DbConnect {
		    // specify your own database credentials
    private $host = "LAPTOP-11PJ719G";
    private $db_name = "Onboarduat";
    private $username = "sa";
    private $password = "tr0j@n";
    // public $conn;
		// private $server = 'localhost';
		// private $dbname = 'jwtapi';
		// private $user = 'root';
		// private $pass = 'root';

		public function connect() {
			try {
				$conn = new PDO("sqlsrv:Server=" . $this->host . ";Database=" . $this->db_name, $this->username, $this->password);
				$conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				return $conn;
			} catch (\Exception $e) {
				echo "Database Error: " . $e->getMessage();
			}
		}
	}
 ?>