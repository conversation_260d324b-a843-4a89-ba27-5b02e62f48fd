<?php 
include_once '../Lib/DbConnect.php';

	class MasterRegionModel {
		
		public $tableName = 'master_region';
		public $dbConn;
		
		
		public $SysId;
		public $Created;
		public $Creator;
		public $LastModified;
		public $Modifier;
		public $MappingId;
		public $Title;
		public $OrderNumber;
	
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setMappingId($MappingId) { $this->MappingId = $MappingId; }
		function getMappingId() { return $this->MappingId; }
		
		
		function setTitle($Title) { $this->Title = $Title; }
		function getTitle() { return $this->Title; }
		
		function setOrderNumber($OrderNumber) { $this->OrderNumber = $OrderNumber; }
		function getOrderNumber() { return $this->OrderNumber; }
		
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		public function getMasterRegionByTitle() {
					
					
					$sql = "select * from master_region a where a.Title = :Title";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':Title', $this->Title);
					$stmt->execute();
					$masterregion = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $masterregion;
					
		}
		
		public function getAllMasterRegion() {
					
					$sql = "select * from master_region";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->execute();
					$masterregion = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $masterregion;
		}
		
		public function updateRM() {
			
			$sql = 'update master_region set LastModified=:LastModified,Modifier=:Modifier,MappingId=:MappingId,
													Title=:Title,OrderNumber=:OrderNumber,Category=:Category,
													IsActive=:IsActive where SysId =:SysId';
			
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':LastModified', $this->LastModified);
			$stmt->bindParam(':Modifier', $this->Modifier);
			$stmt->bindParam(':MappingId', $this->MappingId);;
			$stmt->bindParam(':Title', $this->Title);
			$stmt->bindParam(':OrderNumber', $this->OrderNumber);
			$stmt->bindParam(':SysId', $this->SysId);
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
				
		public function insertRM() {
			

			$sql = 'INSERT INTO  master_region (Creator,Created,MappingI,Title,OrderNumber,Category,IsActive) 
												VALUES(:Creator,:Created,:MappingId,:Title,:OrderNumber)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':MappingId', $this->MappingId);
			$stmt->bindParam(':Title', $this->Title);
			$stmt->bindParam(':OrderNumber', $this->OrderNumber);
			
			if($stmt->execute()) {				 
			
				 // $SysId = $this->dbConn->lastInsertId();
				 // return $SysId;
				 
				return true;
				
			} else {
				return false;
			}
		}
	}
 ?>