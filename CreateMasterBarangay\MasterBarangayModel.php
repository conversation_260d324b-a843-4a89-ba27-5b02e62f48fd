<?php 
include_once '../Lib/DbConnect.php';

	class MasterBarangayModel {
		
		public $tableName = 'master_barangay';
		public $dbConn;		
		
		public $SysId;
		public $Created;
		public $Creator;
		public $LastModified;
		public $Modifier;
		public $Title;
		public $OrderNumber;
		public $ZipCode;
		public $ParentId;
	
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setTitle($Title) { $this->Title = $Title; }
		function getTitle() { return $this->Title; }
		
		function setOrderNumber($OrderNumber) { $this->OrderNumber = $OrderNumber; }
		function getOrderNumber() { return $this->OrderNumber; }
		
		function setParentId($ParentId) { $this->ParentId = $ParentId; }
		function getParentId() { return $this->ParentId; }
		
		function setZipCode($ZipCode) { $this->ZipCode = $ZipCode; }
		function getZipCode() { return $this->ZipCode; }
		
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		public function getMasterBarangayByTitle() {
					
					
					$sql = "select * from master_barangay a where a.Title = :Title";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':Title', $this->Title);
					$stmt->execute();
					$masterbarangay = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $masterbarangay;
					
		}
		
		public function getAllMasterBarangay() {
					
					$sql = "select * from master_barangay order by OrderNumber ";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->execute();
					$masterbarangay = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $masterbarangay;
		}
		
		public function updateBM() {
			
			$sql = 'update master_barangay set LastModified=:LastModified,Modifier=:Modifier,
													Title=:Title,OrderNumber=:OrderNumber,ZipCode=:ZipCode,
													ParentId=:ParentId where SysId =:SysId';
			
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':LastModified', $this->LastModified);
			$stmt->bindParam(':Modifier', $this->Modifier);
			$stmt->bindParam(':Title', $this->Title);
			$stmt->bindParam(':OrderNumber', $this->OrderNumber);
			$stmt->bindParam(':ZipCode', $this->ZipCode);
			$stmt->bindParam(':ParentId', $this->ParentId);
			$stmt->bindParam(':SysId', $this->SysId);
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
				
		public function insertBM() {
			

			$sql = 'INSERT INTO  master_barangay (Creator,Created,Title,ParentId,ZipCode,OrderNumber) 
												VALUES(:Creator,:Created,:Title,:ParentId,:ZipCode,:OrderNumber)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':Title', $this->Title);
			$stmt->bindParam(':ParentId', $this->ParentId);
			$stmt->bindParam(':ZipCode', $this->ZipCode);
			$stmt->bindParam(':OrderNumber', $this->OrderNumber);
			
			if($stmt->execute()) {				 
			
				 // $SysId = $this->dbConn->lastInsertId();
				 // return $SysId;
				 
				return true;
				
			} else {
				return false;
			}
		}
	}
 ?>