<?php 
include_once '../Lib/DbConnect.php';

	class Mc_loan_attachment {
		
		
		private $tableName = 'Mc_loan_attachment';
		private $dbConn;
		
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $ParentId;
		private $DocType;
		private $ImageBase64;
		private $Remarks;
	
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setParentId($ParentId) { $this->ParentId = $ParentId; }
		function getParentId() { return $this->ParentId; }
		
		function setDocType($DocType) { $this->DocType = $DocType; }
		function getDocType() { return $this->DocType; }
		
		function setImageBase64($ImageBase64) { $this->ImageBase64 = $ImageBase64; }
		function getImageBase64() { return $this->ImageBase64; }
		
		function setRemarks($Remarks) { $this->Remarks = $Remarks; }
		function getRemarks() { return $this->Remarks; }
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
				
		public function Mc_loan_attachment() {
			
			$sql = 'INSERT INTO  Mc_loan_attachment (Creator,Created,ParentId,DocType,ImageBase64,Remarks) 
														VALUES (:Creator,:Created,:ParentId,:DocType,:ImageBase64,:Remarks)';
	
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':ParentId', $this->ParentId);
			$stmt->bindParam(':DocType', $this->DocType);
			$stmt->bindParam(':ImageBase64', $this->ImageBase64);
			$stmt->bindParam(':Remarks', $this->Remarks);
						
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>