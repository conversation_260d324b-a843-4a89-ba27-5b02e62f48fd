<?php 
	include_once '../Lib/jwt.php';
	include_once 'restUserDetail.php';
	
	class UserDetail extends RestUser {
		
		public function __construct() {
			parent::__construct();
		}

		public function addUser() {
			
			$data = json_decode($this->request, true);
			
			foreach($data as $obj){
				
			$FName = $this->validateParameter('FName', $obj['FName'], STRING, false);
			$MName = $this->validateParameter('MName', $obj['MName'], STRING, false);
			$LName = $this->validateParameter('LName', $obj['LName'], STRING, false);
			$Position = $this->validateParameter('Position', $obj['Position'], INTEGER, false);
			$UserName = $this->validateParameter('UserName', $obj['UserName'], STRING, false);
			$Password  = $this->validateParameter('Password', $obj['Password'], STRING, false);
			$Flag  = $this->validateParameter('Flag', $obj['Flag'], INTEGER, false);
			$LogIn  = $this->validateParameter('LogIn', $obj['LogIn'], STRING, false);
			
			$pass= hash('sha512', hash('sha256', md5(htmlspecialchars($Password,ENT_QUOTES))));
			$hash_uname= hash('sha512', hash('sha256', md5(htmlspecialchars($UserName,ENT_QUOTES))));
			
			$days_activ = 90;	
			$tday = date('Y-m-d');
			$today = date('Y-m-d', strtotime($tday. ' + ' .$days_activ.' days'));
		
			$user = new UserModel;
			$user->setFName($FName);
			$user->setMName($MName);
			$user->setLName($LName);
			$user->setEmployeeName($FName." ".$MName." ".$LName);
			$user->setPosition($Position);
			$user->setUserName($UserName);
			$user->setPassword($pass);
			$user->setDateCreated(date('Y-m-d'));
			$user->setFlag($Flag);
			$user->setLogIn($LogIn);
			$user->setExpiryDate($today);
			$user->sethash_uname($hash_uname);
			
			if(!$user->insert()) {
				$message = 'Failed to insert.';
			} else {
				$message = "Inserted successfully.";
			}
			
		  }
			$this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function getUserDetailsById() {
			
			if(!isset($_GET['UserID']) || $_GET['UserID'] == "") {
				
				$user = new UserModel;
				$user = $user->getAlluser();
				$this->returnResponseGetAll($user);				
				
			}else{
			
				$UserID = $this->validateParameter('UserID', $_GET['UserID'], INTEGER);
			
				$user = new UserModel;
				$user->setUserID($UserID);
				
				$user = $user->getuserDetailsById();
				if(!is_array($user)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $user);
			}
			
		}
	}
	
 ?>