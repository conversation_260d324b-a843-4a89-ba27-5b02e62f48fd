<?php 
	include_once '../Lib/jwt.php';
	include_once 'restMasterBarangayDetail.php';
	
	class MasterBarangayDetail extends RestMasterBarangay {
		
		public function __construct() {
			parent::__construct();
		}

		public function addMasterBarangay() {
			
			
			$data = json_decode($this->request, true);
			
			foreach($data as $obj){
				
			//$MappingId = $this->validateParameter('MappingId', $obj['MappingId'], STRING, false);
			$Title = $this->validateParameter('Title', $obj['Title'], STRING, false);
			$ParentId = $this->validateParameter('ParentId', $obj['ParentId'], STRING, false);
			//$IncomeClass = $this->validateParameter('IncomeClass', $obj['IncomeClass'], STRING, false);
			$OrderNumber = $this->validateParameter('OrderNumber', $obj['OrderNumber'], INTEGER, false);
			
			
			$mrm = new MasterBarangayModel;
			//$mrm->setMappingId($MappingId);
			$mrm->setTitle($Title);
			$mrm->setParentId($ParentId);
			//$mrm->setIncomeClass($IncomeClass);
			$mrm->setOrderNumber($OrderNumber);
			$mrm->setCreated(date('Y-m-d'));
			$mrm->setCreator($this->userId);
			
			
			if(!$mrm->insertBM()) {
				
				 $message = 'Failed to insert.';
			} else {
							
				$message = 'Inserted successful.';
				  
				// $message = $mrm->insertLV();
			}
			
		  }
			  $this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function updateMasterBarangay() {
			
			
			$data = json_decode($this->request, true);
			
			foreach($data as $obj){
				
			$MappingId = $this->validateParameter('MappingId', $obj['MappingId'], STRING, false);
			$Title = $this->validateParameter('Title', $obj['Title'], STRING, false);
			$ParentId = $this->validateParameter('ParentId', $obj['ParentId'], STRING, false);
			$IncomeClass = $this->validateParameter('IncomeClass', $obj['IncomeClass'], STRING, false);
			$OrderNumber = $this->validateParameter('OrderNumber', $obj['OrderNumber'], INTEGER, false);
			
			
			$mrm = new MasterBarangayModel;
			$mrm->setSysId($_GET['SysId']);
			$mrm->setMappingId($MappingId);
			$mrm->setTitle($Title);
			$mrm->setParentId($ParentId);
			$mrm->setIncomeClass($IncomeClass);
			$mrm->setOrderNumber($OrderNumber);
			$mrm->setLastModified(date('Y-m-d'));
			$mrm->setModifier($this->userId);
			
			
			if(!$mrm->updateBM()) {
				$message = 'Failed to update.';
			} else {
				$message = "Update successfully.";
			}
			
		  }
			$this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function getMasterBarangayDetails() {
			
			if(!isset($_GET['Title']) || $_GET['Title'] == "") {
				
				$masterbarangay = new MasterBarangayModel;
				$masterbarangay = $masterbarangay->getAllMasterBarangay();
				//$this->returnResponseGetAll($masterbarangay);	
				$this->returnResponse(SUCCESS_RESPONSE, $masterbarangay);				
				
			}else{
			
				$Title = $this->validateParameter('Title', $_GET['Title'], STRING);
			
				$masterbarangay = new MasterBarangayModel;
				$masterbarangay->setUserID($Title);
				
				$masterbarangay = $masterbarangay->getMasterBarangayByTitle();
				if(!is_array($masterbarangay)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $masterbarangay);
			}
			
		}
	}
	
 ?>