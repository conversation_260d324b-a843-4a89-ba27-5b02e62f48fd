<?php 
include_once '../Lib/DbConnect.php';

	class ftv_job_model {
		
		
		private $tableName = 'ftv_job';
		private $dbConn;
			
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $ApplicationID;
		private $BlkNumber;
		private $Street;
		private $Subdivision;
		private $Barangay;
		private $City;
		private $Province;
		private $Type;
		private $Category;
		private $Classification;
		private $DateTimeStarted;
		private $DateTimeCompleted;
		private $CreditInvestigator;
		private $Status;
		private $Submit_Lat;
		private $Submit_Lon;
		private $DoneFTV_Lat;
		private $DoneFTV_Lon;
		private $Remarks;
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setDoneFTV_Lat($DoneFTV_Lat) { $this->DoneFTV_Lat = $DoneFTV_Lat; }
		function getDoneFTV_Lat() { return $this->DoneFTV_Lat; }
		
		function setDoneFTV_Lon($DoneFTV_Lon) { $this->DoneFTV_Lon = $DoneFTV_Lon; }
		function getDoneFTV_Lon() { return $this->DoneFTV_Lon; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setApplicationID($ApplicationID) { $this->ApplicationID = $ApplicationID; }
		function getApplicationID() { return $this->ApplicationID; }
		
		function setBlkNumber($BlkNumber) { $this->BlkNumber = $BlkNumber; }
		function getBlkNumber() { return $this->BlkNumber; }
		
		function setStreet($Street) { $this->Street = $Street; }
		function getStreet() { return $this->Street; }
		
		function setSubdivision($Subdivision) { $this->Subdivision = $Subdivision; }
		function getSubdivision() { return $this->Subdivision; }
		
		function setBarangay($Barangay) { $this->Barangay = $Barangay; }
		function getBarangay() { return $this->Barangay; }
		
		function setCity($City) { $this->City = $City; }
		function getCity() { return $this->City; }
		
		function setProvince($Province) { $this->Province = $Province; }
		function getProvince() { return $this->Province; }
		
		function setType($Type) { $this->Type = $Type; }
		function getType() { return $this->Type; }
		
		function setCategory($Category) { $this->Category = $Category; }
		function getCategory() { return $this->Category; }
		
		function setClassification($Classification) { $this->Classification = $Classification; }
		function getClassification() { return $this->Classification; }
		
		function setDateTimeCompleted($DateTimeCompleted) { $this->DateTimeCompleted = $DateTimeCompleted; }
		function getDateTimeCompleted() { return $this->DateTimeCompleted; }
		
		function setDateTimeStarted($DateTimeStarted) { $this->DateTimeStarted = $DateTimeStarted; }
		function getDateTimeStarted() { return $this->DateTimeStarted; }
		
		function setCreditInvestigator($CreditInvestigator) { $this->CreditInvestigator = $CreditInvestigator; }
		function getCreditInvestigator() { return $this->CreditInvestigator; }
		
		function setStatus($Status) { $this->Status = $Status; }
		function getStatus() { return $this->Status; }
				
		function setSubmit_Lat($Submit_Lat) { $this->Submit_Lat = $Submit_Lat; }
		function getSubmit_Lat() { return $this->Submit_Lat; }
				
		function setSubmit_Lon($Submit_Lon) { $this->Submit_Lon = $Submit_Lon; }
		function getSubmit_Lon() { return $this->Submit_Lon; }
				
		function setRemarks($Remarks) { $this->Remarks = $Remarks; }
		function getRemarks() { return $this->Remarks; }
	
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
		
		public function updateftvjob() {
			
			$sql = 'update ftv_job set LastModified=:LastModified,Modifier=:Modifier,Status=:Status,DoneFTV_Lat=:DoneFTV_Lat,DoneFTV_Lon=:DoneFTV_Lon,DateTimeCompleted=:DateTimeCompleted 
					where SysId =:SysId';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':LastModified', $this->LastModified);
			$stmt->bindParam(':Modifier', $this->Modifier);
			$stmt->bindParam(':Status', $this->Status);
			$stmt->bindParam(':DoneFTV_Lat', $this->DoneFTV_Lat);
			$stmt->bindParam(':DoneFTV_Lon', $this->DoneFTV_Lon);
			$stmt->bindParam(':DateTimeCompleted', $this->DateTimeCompleted);
			$stmt->bindParam(':SysId', $this->SysId);
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
		
		public function updateftvjobstarted() {
			
			$sql = 'update ftv_job set LastModified=:LastModified,Modifier=:Modifier,Status=:Status,DateTimeStarted=:DateTimeStarted 
					where SysId =:SysId';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':LastModified', $this->LastModified);
			$stmt->bindParam(':Modifier', $this->Modifier);
			$stmt->bindParam(':Status', $this->Status);
			$stmt->bindParam(':DateTimeStarted', $this->DateTimeStarted);
			$stmt->bindParam(':SysId', $this->SysId);
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
		
		public function Getpostcistatus() {
			
					$SysIdVhic = $this->SysId;
					
					$sql = "select count(1) bilang from ftv_job where Status in ('JOB STARTED','JOB CREATED') 
							and ApplicationID in (select ApplicationID from ftv_job where SysId = :SysId)";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':SysId', $this->SysId);
					$stmt->execute();
					$Getpostcistatuss = $stmt->fetch(PDO::FETCH_ASSOC);
				
				if(!is_array($Getpostcistatuss)) {
					
				}
					
					if ($Getpostcistatuss['bilang'] == 0){
						
						$sql = "update  Mc_loan_application set status = 'POST CI' 
						where SysId in (select ApplicationID from ftv_job where SysId =:SysId)";
						$stmt = $this->dbConn->prepare($sql);
						$stmt->bindParam(':SysId', $SysIdVhic);
						
						if($stmt->execute()){
							return true;
						}else{
							return false;
						}
						
					}else{
						//return $Getpostcistatuss;
					}
					
		}

				
		public function ftv_job() {
			
			$sql = 'INSERT INTO  ftv_job (Creator,Created,ApplicationID,BlkNumber,Street,Subdivision,Barangay,City,Province,Type,Category) 
								   VALUES(:Creator,:Created,:ApplicationID,:BlkNumber,:Street,:Subdivision,:Barangay,:City,:Province,:Type,:Category)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':ApplicationID', $this->ApplicationID);
			$stmt->bindParam(':BlkNumber', $this->BlkNumber);
			$stmt->bindParam(':Street', $this->Street);
			$stmt->bindParam(':Subdivision', $this->Subdivision);
			$stmt->bindParam(':Barangay', $this->Barangay);
			$stmt->bindParam(':City', $this->City);
			$stmt->bindParam(':Province', $this->Province);
			$stmt->bindParam(':Type', $this->Type);
			$stmt->bindParam(':Category', $this->Category);
			//$stmt->bindParam(':Classification', $this->Classification);
						
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>