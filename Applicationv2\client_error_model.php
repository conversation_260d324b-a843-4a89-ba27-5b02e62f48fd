<?php 
include_once '../Lib/DbConnect.php';

	class client_error_model{
		

		private $ClientId;

		function setClientId($ClientId) { $this->ClientId = $ClientId; }
		function getClientId() { return $this->ClientId; }
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
				
		public function client_error_model() {
			
			$client_indv_personal_info = 'delete from client_indv_personal_info where SysId =:ClientId';
			$client_indv_personal_info = $this->dbConn->prepare($client_indv_personal_info);
			$client_indv_personal_info->bindParam(':ClientId', $this->ClientId);
						
			if($client_indv_personal_info->execute()) {
				//return true;
			} else {
				//return false;
			}
				
			$Mc_loan_application = 'delete from Mc_loan_application where ClientInfoId =:ClientId';
			$Mc_loan_application = $this->dbConn->prepare($Mc_loan_application);
			$Mc_loan_application->bindParam(':ClientId', $this->ClientId);
						
			if($Mc_loan_application->execute()) {
				//return true;
			} else {
				//return false;
			}
			
			$Mc_loan_attachment = 'delete from Mc_loan_attachment where ParentId =:ClientId';
			$Mc_loan_attachment = $this->dbConn->prepare(Mc_loan_attachment);
			$Mc_loan_attachment->bindParam(':ClientId', $this->ClientId);
						
			if($Mc_loan_attachment->execute()) {
				//return true;
			} else {
				//return false;
			}
			
			$client_indv_spouse = 'delete from client_indv_spouse where ClientId =:ClientId';
			$client_indv_spouse = $this->dbConn->prepare($client_indv_spouse);
			$client_indv_spouse->bindParam(':ClientId', $this->ClientId);
						
			if($client_indv_spouse->execute()) {
				//return true;
			} else {
				//return false;
			}
			
			$client_indv_address_current = 'delete from client_indv_address_current where ClientId =:ClientId';
			$client_indv_address_current = $this->dbConn->prepare($client_indv_address_current);
			$client_indv_address_current->bindParam(':ClientId', $this->ClientId);
						
			if($client_indv_address_current->execute()) {
				//return true;
			} else {
				//return false;
			}
			
			$client_indv_address_permanent = 'delete from client_indv_address_permanent where ClientId =:ClientId';
			$client_indv_address_permanent = $this->dbConn->prepare($client_indv_address_permanent);
			$client_indv_address_permanent->bindParam(':ClientId', $this->ClientId);
						
			if($client_indv_address_permanent->execute()) {
				//return true;
			} else {
				//return false;
			}
			
			$client_indv_character_reference = 'delete from client_indv_character_reference where ClientId =:ClientId';
			$client_indv_character_reference = $this->dbConn->prepare($client_indv_character_reference);
			$client_indv_character_reference->bindParam(':ClientId', $this->ClientId);
						
			if($client_indv_character_reference->execute()) {
				//return true;
			} else {
				//return false;
			}
			
			$client_indv_identification = 'delete from client_indv_identification where ClientId =:ClientId';
			$client_indv_identification = $this->dbConn->prepare($client_indv_identification);
			$client_indv_identification->bindParam(':ClientId', $this->ClientId);
						
			if($client_indv_identification->execute()) {
				//return true;
			} else {
				//return false;
			}
			
			
			
			
			$client_indv_source_of_income = 'delete from client_indv_source_of_income where ClientId =:ClientId';
			$client_indv_source_of_income = $this->dbConn->prepare($client_indv_source_of_income);
			$client_indv_source_of_income->bindParam(':ClientId', $this->ClientId);
						
			if($client_indv_source_of_income->execute()) {
				//return true;
			} else {
				//return false;
			}
			
			$client_indv_soi_business = 'delete from client_indv_soi_business where  ParentId in (select SysId from client_indv_source_of_income where ClientId =:ClientId)';
			$client_indv_soi_business = $this->dbConn->prepare($client_indv_soi_business);
			$client_indv_soi_business->bindParam(':ClientId', $this->ClientId);
						
			if($client_indv_soi_business->execute()) {
				//return true;
			} else {
				//return false;
			}
			
			
			$client_indv_soi_goverment_employee = 'delete from client_indv_soi_goverment_employee where  ParentId in (select SysId from client_indv_source_of_income where ClientId =:ClientId)';
			$client_indv_soi_goverment_employee = $this->dbConn->prepare($client_indv_soi_goverment_employee);
			$client_indv_soi_goverment_employee->bindParam(':ClientId', $this->ClientId);
						
			if($client_indv_soi_goverment_employee->execute()) {
				//return true;
			} else {
				//return false;
			}
			
			$client_indv_soi_pension = 'delete from client_indv_soi_pension where  ParentId in (select SysId from client_indv_source_of_income where ClientId =:ClientId)';
			$client_indv_soi_pension = $this->dbConn->prepare($client_indv_soi_pension);
			$client_indv_soi_pension->bindParam(':ClientId', $this->ClientId);
						
			if($client_indv_soi_pension->execute()) {
				//return true;
			} else {
				//return false;
			}
			
			$client_indv_soi_private_employee = 'delete from client_indv_soi_private_employee where  ParentId in (select SysId from client_indv_source_of_income where ClientId =:ClientId)';
			$client_indv_soi_private_employee = $this->dbConn->prepare($client_indv_soi_private_employee);
			$client_indv_soi_private_employee->bindParam(':ClientId', $this->ClientId);
						
			if($client_indv_soi_private_employee->execute()) {
				//return true;
			} else {
				//return false;
			}
			
			$client_indv_soi_remittance = 'delete from client_indv_soi_remittance where  ParentId in (select SysId from client_indv_source_of_income where ClientId =:ClientId)';
			$client_indv_soi_remittance = $this->dbConn->prepare($client_indv_soi_remittance);
			$client_indv_soi_remittance->bindParam(':ClientId', $this->ClientId);
						
			if($client_indv_soi_remittance->execute()) {
				//return true;
			} else {
				//return false;
			}
			
			$client_indv_soi_selfemployed = 'delete from client_indv_soi_selfemployed where  ParentId in (select SysId from client_indv_source_of_income where ClientId =:ClientId)';
			$client_indv_soi_selfemployed = $this->dbConn->prepare($client_indv_soi_selfemployed);
			$client_indv_soi_selfemployed->bindParam(':ClientId', $this->ClientId);
						
			if($client_indv_soi_selfemployed->execute()) {
				//return true;
			} else {
				//return false;
			}
			
			
		}
	}
 ?>