<?php 
	include_once '../Lib/jwt.php';
	include_once 'restDealerPricingDetail.php';
	
	class DealerPricingDetail extends RestDealerPricing {
		
		public function __construct() {
			parent::__construct();
		}

		
		
		public function getDealerPricingDetails() {
			
			if(!isset($_GET['Title']) || $_GET['Title'] == "") {
				
				$DealerPricingModel = new DealerPricingModel;
				$DealerPricingModel = $DealerPricingModel->getAllDealerPricing();
				$this->returnResponseGetAll($DealerPricingModel);				
				
			}else{
			
				$Title = $this->validateParameter('Title', $_GET['Title'], STRING);
			
				$DealerPricingModel = new DealerPricingModel;
				$DealerPricingModel->setTitle($Title);
				
				$DealerPricingModel = $DealerPricingModel->getDealerPricingByTitle();
				if(!is_array($DealerPricingModel)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $DealerPricingModel);
			}
			
		}
	}
	
 ?>