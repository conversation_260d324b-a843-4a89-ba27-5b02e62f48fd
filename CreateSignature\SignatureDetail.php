<?php 
	include_once '../Lib/jwt.php';
	include_once 'restSignatureDetail.php';
	
	class SignatureDetail extends RestSignature {
		
		public function __construct() {
			parent::__construct();
		}

		public function addSignature() {
			
			
			$data = json_decode($this->request, true);
			
			foreach($data as $obj){
								
			$ApplicationID = $this->validateParameter('ApplicationID', $obj['applicationID'], STRING);
			$DocumentCode = $this->validateParameter('DocumentCode', $obj['documentCode'], STRING);
			$ImageBase64 = $this->validateParameter('ImageBase64', $obj['imageBase64'], STRING);
			$IsUsed = $this->validateParameter('IsUsed', $obj['isUsed'], BOOLEAN, false);
			
			$mrm = new CreateSignatureModel;
			$mrm->setApplicationID($ApplicationID);
			$mrm->setDocumentCode($DocumentCode);
			$mrm->setImageBase64($ImageBase64);
			$mrm->setIsUsed($IsUsed);
			$mrm->setCreated(date('Y-m-d'));
			$mrm->setCreator($this->userId);
			
			
			if(!$mrm->insertSignature()) {
				
				 $message = 'Failed to insert.';
			} else {
							
				$message = 'Inserted successful.';
				  
			}
			
		  }
			  $this->returnResponse(SUCCESS_RESPONSE, $message);
		}
	}
	
 ?>