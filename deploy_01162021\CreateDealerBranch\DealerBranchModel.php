<?php 
include_once '../Lib/DbConnect.php';

	class DealerBranchModel {
		
		public $tableName = 'DealerBranch';
		public $dbConn;
		
		public $SysId;
		public $Created;
		public $Creator;
		public $LastModified;
		public $Modifier;
		public $DealerKey;
		public $Title;
		public $Email;
		public $Code;
		public $DealerGroup;
		public $City;
		public $Province;
		public $Barangay;
		public $Street;
		public $Building;
		public $Longtitude;
		public $Latitude;
		public $MobileNumber;
		public $LandlineNumber;
		public $AreaCode;
		public $IsActive;
		
	
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setDealerKey($DealerKey) { $this->DealerKey = $DealerKey; }
		function getDealerKey() { return $this->DealerKey; }
		
		function setTitle($Title) { $this->Title = $Title; }
		function getTitle() { return $this->Title; }
		
		function setEmail($Email) { $this->Email = $Email; }
		function getEmail() { return $this->Email; }
				
		function setCode($Code) { $this->Code = $Code; }
		function getCode() { return $this->Code; }
	
		function setDealerGroup($DealerGroup) { $this->DealerGroup = $DealerGroup; }
		function getDealerGroup() { return $this->DealerGroup; }
	
		function setCity($City) { $this->City = $City; }
		function getCity() { return $this->City; }
	
		function setProvince($Province) { $this->Province = $Province; }
		function getProvince() { return $this->Province; }
	
		function setBarangay($Barangay) { $this->Barangay = $Barangay; }
		function getBarangay() { return $this->Barangay; }
	
		function setStreet($Street) { $this->Street = $Street; }
		function getStreet() { return $this->Street; }
	
		function setBuilding($Building) { $this->Building = $Building; }
		function getBuilding() { return $this->Building; }
	
		function setLongtitude($Longtitude) { $this->Longtitude = $Longtitude; }
		function getLongtitude() { return $this->Longtitude; }
	
		function setLatitude($Latitude) { $this->Latitude = $Latitude; }
		function getLatitude() { return $this->Latitude; }
	
		function setMobileNumber($MobileNumber) { $this->MobileNumber = $MobileNumber; }
		function getMobileNumber() { return $this->MobileNumber; }
	
		function setLandlineNumber($LandlineNumber) { $this->LandlineNumber = $LandlineNumber; }
		function getLandlineNumber() { return $this->LandlineNumber; }
	
		function setAreaCode($AreaCode) { $this->AreaCode = $AreaCode; }
		function getAreaCode() { return $this->AreaCode; }
	
		function setIsActive($IsActive) { $this->IsActive = $IsActive; }
		function getIsActive() { return $this->IsActive; }
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		public function getDealerBranchByTitle() {
					
					
					$sql = "select * from DealerBranch a where a.Title = :Title";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':Title', $this->Title);
					$stmt->execute();
					$DealerBranch = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $DealerBranch;
					
		}
		
		public function getAllDealerBranch() {
					
					$sql = "select * from DealerBranch";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->execute();
					$DealerBranch = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $DealerBranch;
		}
	}
 ?>