<?php 
include_once '../Lib/DbConnect.php';

	class ftv_job_result_model {
		
		
		private $tableName = 'ftv_job_result';
		private $dbConn;
			
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $JobId;
		private $FullName;
		private $Relationship;
		
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setRelationship($Relationship) { $this->Relationship = $Relationship; }
		function getRelationship() { return $this->Relationship; }

		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setJobId($JobId) { $this->JobId = $JobId; }
		function getJobId() { return $this->JobId; }
				
		function setFullName($FullName) { $this->FullName = $FullName; }
		function getFullName() { return $this->FullName; }
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
		

				
		public function ftv_job_result() {
			
			$sql = 'INSERT INTO  ftv_job_result (Creator,Created,JobId,FullName,Relationship) 
								 VALUES(:Creator,:Created,:JobId,:FullName,:Relationship)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':JobId', $this->JobId);
			$stmt->bindParam(':FullName', $this->FullName);
			$stmt->bindParam(':Relationship', $this->Relationship);
	
			if($stmt->execute()) {
				
				
			//	$this->Getpostcistatus();

				return true;
			} else {
				return false;
			}
		}
		
		
	}
 ?>