<?php 
include_once '../Lib/DbConnect.php';

	class mc_application_score {
		
		
		private $tableName = 'mc_application_score';
		private $dbConn;

		private $ScoreType;
		private $ScoreValue;
		private $InputValue;
		private $Score;
		private $ClientInfoId;
			
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setScoreType($ScoreType) { $this->ScoreType = $ScoreType; }
		function getScoreType() { return $this->ScoreType; }
		
		function setScoreValue($ScoreValue) { $this->ScoreValue = $ScoreValue; }
		function getScoreValue() { return $this->ScoreValue; }
		
		function setInputValue($InputValue) { $this->InputValue = $InputValue; }
		function getInputValue() { return $this->InputValue; }
		
		function setScore($Score) { $this->Score = $Score; }
		function getScore() { return $this->Score; }
		
		function setClientInfoId($ClientInfoId) { $this->ClientInfoId = $ClientInfoId; }
		function getClientInfoId() { return $this->ClientInfoId; }
				
		

		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
		
		public function generate_mc_application_score() {
			
			
			
			$ScoreType = "";
			$ScoreValue = "";
			$Score = "";
			
			$sql = "select ScoreType,ScoreValue,Score from master_scoring where ScoreType =:Type and ScoreValue = :ScoreValue";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':Type', $this->ScoreType);
					$stmt->bindParam(':ScoreValue',$this->ScoreValue);
					$stmt->execute();				
					$ScoreVhic = $stmt->fetch(PDO::FETCH_ASSOC);
				
					if(!is_array($ScoreVhic)) {
					
					}else{
						//echo $ScoreVhic['ScoreValue'];
							$date_today = date('Y-m-d H:i:s');
							$ScoreType = $ScoreVhic['ScoreType'];
							$ScoreValue = $ScoreVhic['ScoreValue'];
							$Score = $ScoreVhic['Score'];
							
						
							$sql = 'INSERT INTO  mc_application_score (Creator,Created,InputValue,Score,ScoreValue,ScoreType,ClientInfoId) 
											  VALUES (:Creator,:Created,:InputValue,:Score,:ScoreValue,:ScoreType,:ClientInfoId)';
			
							$stmt = $this->dbConn->prepare($sql);
							$stmt->bindParam(':Creator', $this->userId);
							$stmt->bindParam(':Created',$date_today);
							$stmt->bindParam(':ScoreType',$ScoreType);
							$stmt->bindParam(':ScoreValue', $ScoreValue);
							$stmt->bindParam(':InputValue', $ScoreValue);
							$stmt->bindParam(':Score', $Score);
							$stmt->bindParam(':ClientInfoId', $this->ClientInfoId);
										
							if($stmt->execute()) {
								
							} else {
								// ssreturn false;
							}
							
					}
		}
	}
 ?>