<?php 
include_once '../Lib/DbConnect.php';

	class banca_application {
		
		private $tableName = 'banca_application';
		private $dbConn;
		
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $ClientId;
		private $ApplicationId;
		private $ClassId;
		private $OptionId;
		private $ApplicationStatus;
		private $ApplicationDate;
		private $Is_reject;
		private $Is_reapply;
		private $CardNo;
		private $AccountNo;
		private $PaymentMethod;
		private $Branch;
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setClientId($ClientId) { $this->ClientId = $ClientId; }
		function getClientId() { return $this->ClientId; }
		
		function setApplicationId($ApplicationId) { $this->ApplicationId = $ApplicationId; }
		function getApplicationId() { return $this->ApplicationId; }
		
		function setClassId($ClassId) { $this->ClassId = $ClassId; }
		function getClassId() { return $this->ClassId; }
		
		function setOptionId($OptionId) { $this->OptionId = $OptionId; }
		function getOptionId() { return $this->OptionId; }
		
		function setApplicationStatus($ApplicationStatus) { $this->ApplicationStatus = $ApplicationStatus; }
		function getApplicationStatus() { return $this->ApplicationStatus; }
		
		function setApplicationDate($ApplicationDate) { $this->ApplicationDate = $ApplicationDate; }
		function getApplicationDate() { return $this->ApplicationDate; }
		
		function setIs_reject($Is_reject) { $this->Is_reject = $Is_reject; }
		function getIs_reject() { return $this->Is_reject; }
		
		function setIs_reapply($Is_reapply) { $this->Is_reapply = $Is_reapply; }
		function getIs_reapply() { return $this->Is_reapply; }
		
		function setCardNo($CardNo) { $this->CardNo = $CardNo; }
		function getCardNo() { return $this->CardNo; }
		
		function setAccountNo($AccountNo) { $this->AccountNo = $AccountNo; }
		function getAccountNo() { return $this->AccountNo; }
		
		function setPaymentMethod($PaymentMethod) { $this->PaymentMethod = $PaymentMethod; }
		function getPaymentMethod() { return $this->PaymentMethod; }
		
		function setBranch($Branch) { $this->Branch = $Branch; }
		function getBranch() { return $this->Branch; }
		

		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
		
		
		public function generate_Banca_application($ClientId) {
			
			$sql = "select DBO.ApplicationNo(:ClientId) as ApplicationId";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':ClientId', $ClientId);
					$stmt->execute();
					// $applicationNo = $stmt->fetch(PDO::FETCH_ASSOC);
					
					
					return $stmt->fetch(PDO::FETCH_ASSOC);;
		}
				
		public function banca_application() {
			
			
			
			$sql = 'INSERT INTO  Banca_application (Creator,Created,ClientId,ApplicationId,ClassId,OptionId,ApplicationStatus,ApplicationDate,PaymentMethod,AccountNo,CardNo,Branch) 
										    VALUES (:Creator,:Created,:ClientId,:ApplicationId,:ClassId,:OptionId,:ApplicationStatus,:ApplicationDate,:PaymentMethod,:AccountNo,:CardNo,:Branch)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':ClientId', $this->ClientId);
			$stmt->bindParam(':ApplicationId', $this->ApplicationId);
			$stmt->bindParam(':ClassId', $this->ClassId);
			$stmt->bindParam(':OptionId', $this->OptionId);
			$stmt->bindParam(':ApplicationStatus', $this->ApplicationStatus);
			$stmt->bindParam(':ApplicationDate', $this->ApplicationDate);
			$stmt->bindParam(':PaymentMethod', $this->PaymentMethod);
			$stmt->bindParam(':AccountNo', $this->AccountNo);
			$stmt->bindParam(':CardNo', $this->CardNo);
			$stmt->bindParam(':Branch', $this->Branch);
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>