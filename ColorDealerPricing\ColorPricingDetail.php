<?php 
	include_once '../Lib/jwt.php';
	include_once 'restColorPricingDetail.php';
	
	class ColorPricingDetail extends RestColorPricing {
		
		public function __construct() {
			parent::__construct();
		}

		
		
		public function getColorPricingDetails() {
			
			if(!isset($_GET['Title']) || $_GET['Title'] == "") {
				
				$ColorPricingModel = new ColorPricingModel;
				$ColorPricingModel = $ColorPricingModel->getAllColorPricing();
				$this->returnResponseGetAll($ColorPricingModel);				
				
			}else{
			
				$Title = $this->validateParameter('Title', $_GET['Title'], STRING);
			
				$ColorPricingModel = new ColorPricingModel;
				$ColorPricingModel->setTitle($Title);
				
				$ColorPricingModel = $ColorPricingModel->getColorPricingByTitle();
				if(!is_array($ColorPricingModel)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $ColorPricingModel);
			}
			
		}
	}
	
 ?>