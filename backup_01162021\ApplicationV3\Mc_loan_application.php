<?php 
include_once '../Lib/DbConnect.php';

	class Mc_loan_application {
		
		
		private $tableName = 'Mc_loan_application';
		private $dbConn;

		private $ApplicationID;
		private $FinancingBranch;
		private $CRSC;
		private $DealerCompany;
		private $DealerBranch;
		private $NumberOfUnit;
		private $Classification;
		private $Brand;
		private $Model;
		private $SellingPrice;
		private $DownPayment;
		private $Term;
		private $MonthlyAmort;
		private $Rebate;
		private $ApplicationDate;
		private $LoanType;
		private $Purpose;
		private $ClientInfoId;
		private $IsRepeatBorrower;
		private $Status;
		private $Channel;
		private $DecisionRemarks;
		private $CEId;
		private $CSId;
		private $VerifierID;
		private $IsDealerUpload;
		private $AppKey;
		private $DealerKey;
		private $ValidationResult;
		private $ValidationDetails;
		private $DateTimeStarted;
		private $DateTimeCompleted;
		private $DateTimeOnBoardStarted;
		private $DateTimeOnPreCICompleted;
		private $DateTimeOnPostCIStarted;
		private $Product;
		private $DSA;
		private $DME;
		private $ChassisNumber;
		private $ColorOfUnit;
		private $EngineNumber;
		
	
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setApplicationID($ApplicationID) { $this->ApplicationID = $ApplicationID; }
		function getApplicationID() { return $this->ApplicationID; }
		
		function setFinancingBranch($FinancingBranch) { $this->FinancingBranch = $FinancingBranch; }
		function getFinancingBranch() { return $this->FinancingBranch; }
		
		function setCRSC($CRSC) { $this->CRSC = $CRSC; }
		function getCRSC() { return $this->CRSC; }
		
		function setDealerCompany($DealerCompany) { $this->DealerCompany = $DealerCompany; }
		function getDealerCompany() { return $this->DealerCompany; }
		
		function setDealerBranch($DealerBranch) { $this->DealerBranch = $DealerBranch; }
		function getDealerBranch() { return $this->DealerBranch; }
		
		function setNumberOfUnit($NumberOfUnit) { $this->NumberOfUnit = $NumberOfUnit; }
		function getNumberOfUnit() { return $this->NumberOfUnit; }
		
		function setClassification($Classification) { $this->Classification = $Classification; }
		function getClassification() { return $this->Classification; }
		
		function setBrand($Brand) { $this->Brand = $Brand; }
		function getBrand() { return $this->Brand; }
		
		function setModel($Model) { $this->Model = $Model; }
		function getModel() { return $this->Model; }
		
		function setSellingPrice($SellingPrice) { $this->SellingPrice = $SellingPrice; }
		function getSellingPrice() { return $this->SellingPrice; }
		
		function setDownPayment($DownPayment) { $this->DownPayment = $DownPayment; }
		function getDownPayment() { return $this->DownPayment; }
		
		function setTerm($Term) { $this->Term = $Term; }
		function getTerm() { return $this->Term; }
		
		function setMonthlyAmort($MonthlyAmort) { $this->MonthlyAmort = $MonthlyAmort; }
		function getMonthlyAmort() { return $this->MonthlyAmort; }
		
		function setRebate($Rebate) { $this->Rebate = $Rebate; }
		function getRebate() { return $this->Rebate; }
		
		function setApplicationDate($ApplicationDate) { $this->ApplicationDate = $ApplicationDate; }
		function getApplicationDate() { return $this->ApplicationDate; }
		
		function setLoanType($LoanType) { $this->LoanType = $LoanType; }
		function getLoanType() { return $this->LoanType; }
		
		function setPurpose($Purpose) { $this->Purpose = $Purpose; }
		function getPurpose() { return $this->Purpose; }
		
		function setClientInfoId($ClientInfoId) { $this->ClientInfoId = $ClientInfoId; }
		function getClientInfoId() { return $this->ClientInfoId; }
		
		function setIsRepeatBorrower($IsRepeatBorrower) { $this->IsRepeatBorrower = $IsRepeatBorrower; }
		function getIsRepeatBorrower() { return $this->IsRepeatBorrower; }
		
		function setStatus($Status) { $this->Status = $Status; }
		function getStatus() { return $this->Status; }
		
		function setChannel($Channel) { $this->Channel = $Channel; }
		function getChannel() { return $this->Channel; }
		
		function setDecisionRemarks($DecisionRemarks) { $this->DecisionRemarks = $DecisionRemarks; }
		function getDecisionRemarks() { return $this->DecisionRemarks; }
		
		function setCEId($CEId) { $this->CEId = $CEId; }
		function getCEId() { return $this->CEId; }
		
		function setCSId($CSId) { $this->CSId = $CSId; }
		function getCSId() { return $this->CSId; }
		
		function setVerifierID($VerifierID) { $this->VerifierID = $VerifierID; }
		function getVerifierID() { return $this->VerifierID; }
		
		function setIsDealerUpload($IsDealerUpload) { $this->IsDealerUpload = $IsDealerUpload; }
		function getIsDealerUpload() { return $this->IsDealerUpload; }
		
		function setAppKey($AppKey) { $this->AppKey = $AppKey; }
		function getAppKey() { return $this->AppKey; }
		
		function setDealerKey($DealerKey) { $this->DealerKey = $DealerKey; }
		function getDealerKey() { return $this->DealerKey; }
		
		function setValidationResult($ValidationResult) { $this->ValidationResult = $ValidationResult; }
		function getValidationResult() { return $this->ValidationResult; }
		
		function setValidationDetails($ValidationDetails) { $this->ValidationDetails = $ValidationDetails; }
		function getValidationDetails() { return $this->ValidationDetails; }
		
		function setDateTimeStarted($DateTimeStarted) { $this->DateTimeStarted = $DateTimeStarted; }
		function getDateTimeStarted() { return $this->DateTimeStarted; }
		
		function setDateTimeCompleted($DateTimeCompleted) { $this->DateTimeCompleted = $DateTimeCompleted; }
		function getDateTimeCompleted() { return $this->DateTimeCompleted; }
		
		function setDateTimeOnBoardStarted($DateTimeOnBoardStarted) { $this->DateTimeOnBoardStarted = $DateTimeOnBoardStarted; }
		function getDateTimeOnBoardStarted() { return $this->DateTimeOnBoardStarted; }
		
		function setDateTimeOnPreCICompleted($DateTimeOnPreCICompleted) { $this->DateTimeOnPreCICompleted = $DateTimeOnPreCICompleted; }
		function getDateTimeOnPreCICompleted() { return $this->DateTimeOnPreCICompleted; }
		
		function setDateTimeOnPostCIStarted($DateTimeOnPostCIStarted) { $this->DateTimeOnPostCIStarted = $DateTimeOnPostCIStarted; }
		function getDateTimeOnPostCIStarted() { return $this->DateTimeOnPostCIStarted; }
		
		function setProduct($Product) { $this->Product = $Product; }
		function getProduct() { return $this->Product; }
		
		function setDSA($DSA) { $this->DSA = $DSA; }
		function getDSA() { return $this->DSA; }
		
		function setDME($DSA) { $this->DME = $DME; }
		function getDME() { return $this->DME; }
		
		function setChassisNumber($ChassisNumber) { $this->ChassisNumber = $ChassisNumber; }
		function getChassisNumber() { return $this->ChassisNumber; }
		
		function setColorOfUnit($ColorOfUnit) { $this->ColorOfUnit = $ColorOfUnit; }
		function getColorOfUnit() { return $this->ColorOfUnit; }
		
		function setEngineNumber($EngineNumber) { $this->EngineNumber = $EngineNumber; }
		function getEngineNumber() { return $this->EngineNumber; }
		
		

		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
		
		public function generate_Mc_loan_application($ClientId) {
			
			$sql = "select DBO.ApplicationNo(:ClientId) as ApplicationId";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':ClientId', $ClientId);
					$stmt->execute();
					// $applicationNo = $stmt->fetch(PDO::FETCH_ASSOC);
					
					
					return $stmt->fetch(PDO::FETCH_ASSOC);;
		}
		
				
		public function Mc_loan_application() {
			
			$sql = 'INSERT INTO  Mc_loan_application (Creator,Created,ApplicationDate,Model,Brand,Channel,Classification,CRSC,DateTimeOnBoardStarted,DealerBranch,DealerCompany,DealerKey,DownPayment,FinancingBranch,IsRepeatBorrower,ApplicationID,SellingPrice,Term,MonthlyAmort,LoanType,Purpose,ClientInfoId,NumberOfUnit,Status,Rebate,ColorOfUnit) 
											  VALUES (:Creator,:Created,:ApplicationDate,:Model,:Brand,:Channel,:Classification,:CRSC,:DateTimeOnBoardStarted,:DealerBranch,:DealerCompany,:DealerKey,:DownPayment,:FinancingBranch,:IsRepeatBorrower,:ApplicationID,:SellingPrice,:Term,:MonthlyAmort,:LoanType,:Purpose,:ClientInfoId,:NumberOfUnit,:Status,:Rebate,:ColorOfUnit)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':ApplicationID', $this->ApplicationID);
			$stmt->bindParam(':FinancingBranch', $this->FinancingBranch);
			$stmt->bindParam(':CRSC', $this->CRSC);
			$stmt->bindParam(':DealerCompany', $this->DealerCompany);
			$stmt->bindParam(':DealerBranch', $this->DealerBranch);
			$stmt->bindParam(':NumberOfUnit', $this->NumberOfUnit);
			$stmt->bindParam(':Classification', $this->Classification);
			$stmt->bindParam(':Brand', $this->Brand);
			$stmt->bindParam(':Model', $this->Model);
			$stmt->bindParam(':ColorOfUnit', $this->ColorOfUnit);
			$stmt->bindParam(':SellingPrice', $this->SellingPrice);
			$stmt->bindParam(':DownPayment', $this->DownPayment);
			$stmt->bindParam(':Term', $this->Term);
			$stmt->bindParam(':MonthlyAmort', $this->MonthlyAmort);
			$stmt->bindParam(':Rebate', $this->Rebate);
			$stmt->bindParam(':ApplicationDate', $this->ApplicationDate);
			$stmt->bindParam(':Channel', $this->Channel);
			$stmt->bindParam(':DateTimeOnBoardStarted', $this->DateTimeOnBoardStarted);
			$stmt->bindParam(':DealerKey', $this->DealerKey);
			$stmt->bindParam(':IsRepeatBorrower', $this->IsRepeatBorrower);
			$stmt->bindParam(':LoanType', $this->LoanType);
			$stmt->bindParam(':ClientInfoId', $this->ClientInfoId);
			$stmt->bindParam(':Status', $this->Status);
			$stmt->bindParam(':Purpose', $this->Purpose);
			
			if($stmt->execute()) {
				
				$SysId = $this->dbConn->lastInsertId();
				return $SysId;
			} else {
				$SysId = null;
				return $SysId;
			}
		}
	}
 ?>