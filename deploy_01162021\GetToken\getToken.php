<?php 
	include_once '../Lib/jwt.php';
	class GetToken extends RestGetToken {
		
		public function __construct() {
			parent::__construct();
		}

		public function generateToken() {
			
			$lg = date('Y-m-d h:i:s');

			$username = $this->validateParameter('username', $this->data['username'], STRING);
			$pass1 = $this->validateParameter('password', $this->data['password'], STRING);
			$pass = sha1(md5(htmlspecialchars($pass1,ENT_QUOTES)));

			try {
				
				// $stmt = $this->dbConn->prepare("SELECT * FROM tbluser WHERE username = :username AND Password = :password");
				$stmt = $this->dbConn->prepare("SELECT UserID,Company,Branch,LName,FName,MName,EmployeeName,Position,UserName,Password,
					DateCreated,GroupID,Flag,LogIn,datediff(day,getdate(),ExpiryDate)Diff,Locked,LastLogIn,LogInAttempt,
					Initialized,ip_add,host_name,mac_add,comp_dtl,cp_number,hash_uname,DealerBranch
				FROM tbluser WHERE UserName= :username and Password=:password");
				$stmt->bindParam(":username", $username);
				$stmt->bindParam(":password", $pass);
				$stmt->execute();
				$user = $stmt->fetch(PDO::FETCH_ASSOC);
				if(!is_array($user)) {
					http_response_code(404);
					$this->throwError('Failed', "Email or Password is incorrect.");
				}
				// if( $user['Flag'] != 1) {
					// http_response_code(404);
					// $this->throwError('Failed', "User is not activated. Please contact to admin.");
				// }else 
				if($user['Diff'] < 1) {
					http_response_code(404);
					$this->throwError('Failed', "User account has been expired!");
				}else if($user['Locked'] == 1) {
					http_response_code(404);
					$this->throwError('Failed', "User Account has been locked!");
				}else if($user['Flag'] == 0) {
					http_response_code(404);
					$this->throwError('Failed', "User Account is pending for activation!");
				}else if($user['Flag'] == 2) {
					http_response_code(404);
					$this->throwError('Failed', "Registration denied!");
				}else if($user['Flag'] == 3) {
					http_response_code(404);
					$this->throwError('Failed', "User Account is deactivated!");
				}
			
							
				$paylod = [
					'iat' => time(),
					'exp' => time() + (120*60),
					'userId' => $user['UserID'],
					'branch' => $user['DealerBranch'],
					'MobileVersion' => '1.0.0',
					'Position' => $user['Position']
				];

				$token = JWT::encode($paylod, SECRETE_KEY);
				
				$data = ['token' => $token];
				$this->returnResponse('Success', $data);
				
				
			} catch (Exception $e) {
				http_response_code(404);
				$this->throwError(JWT_PROCESSING_ERROR, $e->getMessage());
			}
		}
	}
	
 ?>