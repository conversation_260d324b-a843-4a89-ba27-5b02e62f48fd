<?php 
include_once '../Lib/DbConnect.php';

	class ReviewApplicationModel {
		
		
		public $ApplicationId;
	
		function setApplicationId($ApplicationId) { $this->ApplicationId = $ApplicationId; }
		function getApplicationId() { return $this->ApplicationId; }
		
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		public function getReviewApplicationByTitle() {
					
					
					$sql = "select b.SysId,ApplicationID,ApplicationDate,b.FirstName,b.MiddleName,b.LastName,b.SuffixName,c.ImageBase64 from 
							Mc_loan_application a

							left join client_indv_personal_info b
							on a.ClientInfoId = b.SysId

							left join Mc_loan_attachment c
							on b.SysId = c.ParentId
							where ApplicationID =:ApplicationID ";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':ApplicationID', $this->ApplicationID);
					$stmt->execute();
					$ReviewApplication = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $ReviewApplication;
					
		}
		
		public function getAllReviewApplication() {
					
					$sql = "select case when DD.Csb_ApplicationId IS NOT NULL then Csb_Application_Status else case when a.Status = 'NEW' then 'PRE CI EVALUATION' else UPPER(a.Status) end end Status,
							b.SysId,ApplicationID,DD.Csb_ApplicationId,ApplicationDate,b.FirstName,b.MiddleName,
							b.LastName,b.SuffixName,c.ImageBase64,
							case when dd.Csb_Application_Remarks<>'N/A' then upper(dd.Csb_Application_Remarks) else '' end CSB_Remarks from 
							Mc_loan_application a
							
							inner join (select DealerBranch,UserID from tbluser) d
							on  a.Creator = d.UserID

							left join client_indv_personal_info b
							on a.ClientInfoId = b.SysId

							left join Mc_loan_attachment c
							on b.SysId = c.ParentId and c.DocType ='PHOTO - CLIENT'

							LEFT JOIN 
								(select top 1 with ties
							  SysId, Created, Csb_ApplicationId,Csb_Application_Status,Csb_Application_Remarks
							from csb_application_status
							order by row_number() over (partition by Csb_ApplicationId order by Created desc)) DD
							ON DD.Csb_ApplicationId=dbo.udf_GetNumeric(a.CSB_API_RESPONSE)

							where a.ChassisNumber is null and a.EngineNumber is null
							and c.ImageBase64 is not null
							--and a.CSB_API_RESPONSE NOT LIKE '%MESSAGE%'
							and d.DealerBranch in (:ApplicationId)
							and ApplicationDate>='2022-09-01'";
								
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':ApplicationId', $this->ApplicationId);
					$stmt->execute();
					$ReviewApplication = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $ReviewApplication;
					
		}
	}
 ?>