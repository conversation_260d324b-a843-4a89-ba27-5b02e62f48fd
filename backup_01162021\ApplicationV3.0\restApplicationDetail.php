<?php 
	include_once '../Lib/constants.php';
	include_once '../Lib/DbConnect.php';
	include_once '../Lib/jwt.php';
	class RestApplication {
		protected $request;
		protected $serviceName;
		protected $param;
		protected $dbConn;
		protected $userId;
		protected $ClientID;

		public function __construct() {
			
				$request_method = $_SERVER['REQUEST_METHOD'];
			
				$handler = fopen('php://input', 'r');
			
				$this->request = stream_get_contents( $handler);
				
							 $db = new DbConnect;
							 $this->dbConn = $db->connect();	
							 $this->validateToken();														 
						
		 }

		Public function validateRequest() {
			// if($_SERVER['CONTENT_TYPE'] !== 'application/json') {
				// $this->throwError(REQUEST_CONTENTTYPE_NOT_VALID, 'Request content type is not valid');
			// }
			
			$data = json_decode($this->request, true);	
			
			if(!isset($data['client_personal_info']) || !is_array($data['client_personal_info'])) {
				http_response_code(400);
				$this->throwError(API_PARAM_REQUIRED, "client_personal_info is required.");
			}
			$this->client_personal_info = $data['client_personal_info'];
			
			if(!isset($data['comaker_personal_info']) || !is_array($data['comaker_personal_info'])) {
				http_response_code(400);
				$this->throwError(API_PARAM_REQUIRED, "comaker_personal_info is required.");
			}
			$this->comaker_personal_info = $data['comaker_personal_info'];
						
			if(!isset($data['client_address_permanent']) || !is_array($data['client_address_permanent'])) {
				http_response_code(400);
				$this->throwError(API_PARAM_REQUIRED, "client_address_permanent is required.");
			}
			$this->client_address_permanent = $data['client_address_permanent'];
			
			if(!isset($data['client_address_current']) || !is_array($data['client_address_current'])) {
				http_response_code(400);
				$this->throwError(API_PARAM_REQUIRED, "client_address_current is required.");
			}
			$this->client_address_current = $data['client_address_current'];
			
			if(!isset($data['comaker_address_current']) || !is_array($data['comaker_address_current'])) {
				http_response_code(400);
				$this->throwError(API_PARAM_REQUIRED, "comaker_address_current is required.");
			}
			$this->comaker_address_current = $data['comaker_address_current'];
			
			if(!isset($data['client_family_expenditure']) || !is_array($data['client_family_expenditure'])) {
				http_response_code(400);
				$this->throwError(API_PARAM_REQUIRED, "client_family_expenditure is required.");
			}
			$this->client_family_expenditure = $data['client_family_expenditure'];
			
			if(!isset($data['client_character_reference']) || !is_array($data['client_character_reference'])) {
				http_response_code(400);
				$this->throwError(API_PARAM_REQUIRED, "client_character_reference is required.");
			}
			$this->client_character_reference = $data['client_character_reference'];
			
			if(!isset($data['client_household_details']) || !is_array($data['client_household_details'])) {
				http_response_code(400);
				$this->throwError(API_PARAM_REQUIRED, "client_household_details is required.");
			}
			$this->client_household_details = $data['client_household_details'];
			
			if(!isset($data['client_identification']) || !is_array($data['client_identification'])) {
				http_response_code(400);
				$this->throwError(API_PARAM_REQUIRED, "client_identification is required.");
			}
			$this->client_identification = $data['client_identification'];
			
			if(!isset($data['client_indv_source_of_income']) || !is_array($data['client_indv_source_of_income'])) {
				http_response_code(400);
				$this->throwError(API_PARAM_REQUIRED, "client_indv_source_of_income is required.");
			}
			$this->client_indv_source_of_income = $data['client_indv_source_of_income'];
			
			if(!isset($data['client_indv_spouse']) || !is_array($data['client_indv_spouse'])) {
				http_response_code(400);
				$this->throwError(API_PARAM_REQUIRED, "client_indv_spouse is required.");
			}
			$this->client_indv_spouse = $data['client_indv_spouse'];
			
			if(!isset($data['mc_loan_attachment']) || !is_array($data['mc_loan_attachment'])) {
				http_response_code(400);
				$this->throwError(API_PARAM_REQUIRED, "mc_loan_attachment is required.");
			}
			$this->mc_loan_attachment = $data['mc_loan_attachment'];
		}
			
		public function getClientID($value) {
			$this->ClientID = $value;
		}
			
		public function validateParameter($fieldName, $value, $dataType, $required = true) {
			
			
			if($dataType != INTEGER){
				if($required == true && empty($value) == true ) {	
					
					$this->throwError(VALIDATE_PARAMETER_REQUIRED, $fieldName . " parameter is required.");
					http_response_code(400);
					
				}
			}

			switch ($dataType) {
				case BOOLEAN:
					if(!is_bool($value)) {
						
						
						$this->throwError(VALIDATE_PARAMETER_DATATYPE, "Datatype is not valid for " . $fieldName . '. It should be boolean.');
						http_response_code(400);
					}
					break;
				case INTEGER:
					if(!is_numeric($value)) {
						
					
						$this->throwError(VALIDATE_PARAMETER_DATATYPE, "Datatype is not valid for " . $fieldName . '. It should be numeric.');
						http_response_code(400);
					}
					break;

				case STRING:
					if(!is_string($value)) {
						http_response_code(400);
					}
					break;
					
				 // case DATE:
				 
							// $value = str_replace('/', '-', $value);     
							// $stamp = strtotime($value);
							// if (is_numeric($stamp)){  
							   // $month = date( 'm', $stamp ); 
							   // $day   = date( 'd', $stamp ); 
							   // $year  = date( 'Y', $stamp ); 
							  // return checkdate($month, $day, $year); 
							// }  
							// $this->throwError(VALIDATE_PARAMETER_DATATYPE, "Datatype is not valid for " . $fieldName . '. It should be Date format.');
							// http_response_code(400);

					// break;
				
				default:
				
					$this->throwError(VALIDATE_PARAMETER_DATATYPE, "Datatype is not valid for " . $fieldName);
					http_response_code(400);
					break;
			}

			return $value;

		}
		
		public function validateToken() {
			try {
				$token = $this->getBearerToken();
				$payload = JWT::decode($token, SECRETE_KEY, ['HS256']);
				
				$stmt = $this->dbConn->prepare("SELECT UserID,Company,Branch,LName,FName,MName,EmployeeName,Position,UserName,Password,
					DateCreated,GroupID,Flag,LogIn,datediff(day,getdate(),ExpiryDate)Diff,Locked,LastLogIn,LogInAttempt,
					Initialized,ip_add,host_name,mac_add,comp_dtl,cp_number,hash_uname 
				FROM tbluser WHERE UserID = :userId");
				$stmt->bindParam(":userId", $payload->userId);
				$stmt->execute();
				$user = $stmt->fetch(PDO::FETCH_ASSOC);
				if(!is_array($user)) {
					 http_response_code(404);
					$this->returnResponse(INVALID_USER_PASS, "This user is not found in our database.");
				}

				if($user['Diff'] < 1) {
					http_response_code(401);
					$this->throwError('Failed', "User account has been expired!");
				}else if($user['Locked'] == 1) {
					http_response_code(401);
					$this->throwError('Failed', "User Account has been locked!");
				}else if($user['Flag'] == 0) {
					http_response_code(401);
					$this->throwError('Failed', "User Account is pending for activation!");
				}else if($user['Flag'] == 2) {
					http_response_code(401);
					$this->throwError('Failed', "Registration denied!");
				}else if($user['Flag'] == 3) {
					http_response_code(401);
					$this->throwError('Failed', "User Account is deactivated!");
				}
				
				$this->userId = $payload->userId;
				
				 http_response_code(200);
				
			} catch (Exception $e) {
				http_response_code(401);
				$this->throwError(ACCESS_TOKEN_ERRORS, $e->getMessage());
			}
		}

		public function processApplicationDetail() {
			
			if($_SERVER['REQUEST_METHOD'] == 'POST' ){
				 $this->validateRequest();
				// $this->Create_Application();
				 $this->Validate_data();
				 
			}else if ($_SERVER['REQUEST_METHOD'] == 'GET' ){
				$this->getCustomerDetailsById();
				
			}
			
		}

		public function throwError($code, $message) {
			header("content-type: application/json");
			$errorMsg = json_encode(['error' => ['status'=>$code, 'message'=>$message]]);
			echo $errorMsg; exit;
		}

		public function returnResponse($code, $data) {
			header("content-type: application/json");
			// $response = json_encode(['resonse' => ['status' => $code, "result" => $data]]);
			$response = json_encode( ['message' => $code, "data" => $data]);
			echo $response; exit;
		}
		
		public function returnResponseGetAll($data) {
			header("content-type: application/json");
			$response = json_encode($data);
			echo $response; exit;
		}
		
		/**
	    * Get hearder Authorization
	    * */
	    public function getAuthorizationHeader(){
	        $headers = null;
	        if (isset($_SERVER['Authorization'])) {
	            $headers = trim($_SERVER["Authorization"]);
	        }
	        else if (isset($_SERVER['HTTP_AUTHORIZATION'])) { //Nginx or fast CGI
	            $headers = trim($_SERVER["HTTP_AUTHORIZATION"]);
	        } elseif (function_exists('apache_request_headers')) {
	            $requestHeaders = apache_request_headers();
	            // Server-side fix for bug in old Android versions (a nice side-effect of this fix means we don't care about capitalization for Authorization)
	            $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));
	            if (isset($requestHeaders['Authorization'])) {
	                $headers = trim($requestHeaders['Authorization']);
	            }
	        }
	        return $headers;
	    }
	    /**
	     * get access token from header
	     * */
	    public function getBearerToken() {
	        $headers = $this->getAuthorizationHeader();
	        // HEADER: Get the access token from the header
	        if (!empty($headers)) {
	            if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
	                return $matches[1];
	            }
	        }
	        $this->throwError( ATHORIZATION_HEADER_NOT_FOUND, 'Access Token Not found');
	    }
	}
 ?>