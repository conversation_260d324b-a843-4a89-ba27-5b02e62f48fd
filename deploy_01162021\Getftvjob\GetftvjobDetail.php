<?php 
	include_once '../Lib/jwt.php';
	include_once 'restGetftvjobDetail.php';
	
	class GetftvjobDetail extends RestGetftvjob {
		
		public function __construct() {
			parent::__construct();
		}

		
		
		public function getGetftvjobDetails() {
			
			if(!isset($_GET['Title']) || $_GET['Title'] == "") {
				
				$GetftvjobModel = new GetftvjobModel;
				$GetftvjobModel->setCreditInvestigator($this->userId);
				$GetftvjobModel = $GetftvjobModel->getAllftvjob();
				
				$this->returnResponseGetAll($GetftvjobModel);				
				
			}else{
			
				$Title = $this->validateParameter('Title', $_GET['Title'], STRING);
			
				$GetftvjobModel = new GetftvjobModel;
				// $GetftvjobModel->setCreator($this->userId);
				$GetftvjobModel->setTitle($Title);
				
				$GetftvjobModel = $GetftvjobModel->getGetftvjobByTitle();
				if(!is_array($GetftvjobModel)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $GetftvjobModel);
			}
			
		}
	}
	
 ?>