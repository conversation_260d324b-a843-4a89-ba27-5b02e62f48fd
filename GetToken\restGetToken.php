<?php 
// required headers
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
 
	// require_once('./constants.php');
	include_once '../Lib/constants.php';
	include_once '../Lib/DbConnect.php';
	class RestGetToken {
		protected $request;
		protected $serviceName;
		protected $param;
		protected $dbConn;
		protected $userId;

		public function __construct() {
			
			$request_method = $_SERVER['REQUEST_METHOD'];
			  
			if($_SERVER['REQUEST_METHOD'] != 'POST' ){
				$this->throwError(REQUEST_METHOD_NOT_VALID, 'Request Method is not valid.');
			}
							 $handler = fopen('php://input', 'r');
							 $this->request = stream_get_contents($handler);
							//$this->validateRequest();
							 $this->data = json_decode($this->request, true);
							 $db = new DbConnect;
							 $this->dbConn = $db->connect();			 
		}

		Public function validateRequest() {
			if($_SERVER['CONTENT_TYPE'] != 'application/json') {
				http_response_code(401);
				$this->throwError(REQUEST_CONTENTTYPE_NOT_VALID, 'Request content type is not valid');
			}
			$this->data = json_decode($this->request, true);
		}

		public function validateParameter($fieldName, $value, $dataType, $required = true) {
			
			if($required == true && empty($value) == true) {
				http_response_code(404);
				$this->throwError(VALIDATE_PARAMETER_REQUIRED, $fieldName . " parameter is required.");
			}

			switch ($dataType) {
				case BOOLEAN:
					if(!is_bool($value)) {
						http_response_code(404);
						$this->throwError(VALIDATE_PARAMETER_DATATYPE, "Datatype is not valid for " . $fieldName . '. It should be boolean.');
					}
					break;
				case INTEGER:
					if(!is_numeric($value)) {
						http_response_code(404);
						$this->throwError(VALIDATE_PARAMETER_DATATYPE, "Datatype is not valid for " . $fieldName . '. It should be numeric.');
					}
					break;

				case STRING:
					if(!is_string($value)) {
						http_response_code(404);
						$this->throwError(VALIDATE_PARAMETER_DATATYPE, "Datatype is not valid for " . $fieldName . '. It should be string.');
					}
					break;
				
				default:
				http_response_code(404);
					$this->throwError(VALIDATE_PARAMETER_DATATYPE, "Datatype is not valid for " . $fieldName);
					break;
			}

			return $value;

		}

		public function getTokenProcess() {
			$this->generateToken();			
		}

		public function throwError($code, $message) {
			header("content-type: application/json");
			$errorMsg = json_encode(['error' => ['status'=>$code, 'message'=>$message]]);
			echo $errorMsg; exit;
		}

		public function returnResponse($code, $data) {
			header("content-type: application/json");
			// $response = json_encode(['resonse' => ['status' => $code, "result" => $data]]);
			$response = json_encode( ['message' => $code, "data" => $data]);
			echo $response; exit;
		}
	}
 ?>