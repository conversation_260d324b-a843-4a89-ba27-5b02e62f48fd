<?php 
include_once '../Lib/DbConnect.php';

	class ReviewApplicationModel {
		
		
		public $ApplicationId;
	
		function setApplicationId($ApplicationId) { $this->ApplicationId = $ApplicationId; }
		function getApplicationId() { return $this->ApplicationId; }
		
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		public function getReviewApplicationByTitle() {
					
					
					$sql = "select b.SysId,ApplicationID,ApplicationDate,b.FirstName,b.MiddleName,b.LastName,b.SuffixName,c.ImageBase64 from 
							Mc_loan_application a

							left join client_indv_personal_info b
							on a.ClientInfoId = b.SysId

							left join Mc_loan_attachment c
							on b.SysId = c.ParentId
							
							where ApplicationID =:ApplicationID ";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':ApplicationID', $this->ApplicationID);
					$stmt->execute();
					$ReviewApplication = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $ReviewApplication;
					
		}
		
		public function getAllReviewApplication() {
					
					$sql = "select b.SysId,ApplicationID,ApplicationDate,b.FirstName,b.MiddleName,b.LastName,b.SuffixName,c.ImageBase64 from 
							Mc_loan_application a
							
							inner join (select DealerBranch,UserID from tbluser) d
							on  a.Creator = d.UserID

							left join client_indv_personal_info b
							on a.ClientInfoId = b.SysId

							left join Mc_loan_attachment c
							on b.SysId = c.ParentId and c.DocType ='PHOTO - CLIENT'
							where a.ChassisNumber is null and a.EngineNumber is null
							and d.DealerBranch in (:ApplicationId)";
								
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':ApplicationId', $this->ApplicationId);
					$stmt->execute();
					$ReviewApplication = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $ReviewApplication;
					
		}
	}
 ?>