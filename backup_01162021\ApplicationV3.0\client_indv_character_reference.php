<?php 
include_once '../Lib/DbConnect.php';

	class client_indv_character_reference {
		
		
		private $tableName = 'client_indv_character_reference';
		private $dbConn;

		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $ClientId;
		private $FullName;
		private $Relationship;
		private $ContactNumber;
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setClientId($ClientId) { $this->ClientId = $ClientId; }
		function getClientId() { return $this->ClientId; }
		
		function setFullName($FullName) { $this->FullName = $FullName; }
		function getFullName() { return $this->FullName; }
		
		function setRelationship($Relationship) { $this->Relationship = $Relationship; }
		function getRelationship() { return $this->Relationship; }
		
		function setContactNumber($ContactNumber) { $this->ContactNumber = $ContactNumber; }
		function getContactNumber() { return $this->ContactNumber; }
		
	
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
				
		public function client_indv_character_reference() {
			
			$sql = 'INSERT INTO  client_indv_character_reference (Creator,Created,ClientId,FullName,Relationship,ContactNumber) 
														  VALUES (:Creator,:Created,:ClientId,:FullName,:Relationship,:ContactNumber)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':ClientId', $this->ClientId);
			$stmt->bindParam(':FullName', $this->FullName);
			$stmt->bindParam(':Relationship', $this->Relationship);
			$stmt->bindParam(':ContactNumber', $this->ContactNumber);
						
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>