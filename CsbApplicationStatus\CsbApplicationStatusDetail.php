<?php 
	include_once '../Lib/jwt.php';
	include_once 'restCsbApplicationStatusDetail.php';
	
	class CsbApplicationStatusDetail extends RestCsbApplicationStatus {
		
		public function __construct() {
			parent::__construct();
		}

		public function addCsbApplicationStatus() {
			
			$data = json_decode($this->request, true);
			
			//foreach($data as $obj){
				
			$Csb_ApplicationId = $this->validateParameter('Csb_ApplicationId', $data['Csb_ApplicationId'], STRING, true );
			$Csb_Application_Status = $this->validateParameter('Csb_Application_Status', $data['Csb_Application_Status'], STRING,true );
			$Csb_Application_Remarks = $this->validateParameter('Csb_Application_Remarks', $data['Csb_Application_Remarks'], STRING,true );
			
			$lvm = new CsbApplicationStatusModel;
			$lvm->setCsb_ApplicationId($Csb_ApplicationId);
			$lvm->setCsb_Application_Status($Csb_Application_Status);
			$lvm->setCsb_Application_Remarks($Csb_Application_Remarks);
			$lvm->setCreated(date('Y-m-d  H:i:s'));
			$lvm->setCreator($this->userId);
			
			
			if(!$lvm->insertLV()) {
				
				 $message = 'Failed to insert.';
			} else {
							
				$message = 'Inserted successful.';
				  
				// $message = $lvm->insertLV();
			}
			
		//  }
			  $this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
	}
	
 ?>