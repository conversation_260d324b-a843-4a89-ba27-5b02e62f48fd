<?php 
	include_once '../Lib/jwt.php';
	include_once 'restMasterRegionDetail.php';
	
	class MasterRegionDetail extends RestMasterRegion {
		
		public function __construct() {
			parent::__construct();
		}

		public function addLookupValues() {
			
			
			$data = json_decode($this->request, true);
			
			foreach($data as $obj){
				
			$MappingId = $this->validateParameter('MappingId', $obj['MappingId'], STRING, false);
			$Title = $this->validateParameter('Title', $obj['Title'], STRING, false);
			$OrderNumber = $this->validateParameter('OrderNumber', $obj['OrderNumber'], INTEGER, false);
			
			
			$mrm = new MasterRegionModel;
			$mrm->setMappingId($MappingId);
			$mrm->setTitle($Title);
			$mrm->setOrderNumber($OrderNumber);
			$mrm->setCreated(date('Y-m-d'));
			$mrm->setCreator($this->userId);
			
			
			if(!$mrm->insertRM()) {
				
				 $message = 'Failed to insert.';
			} else {
							
				$message = 'Inserted successful.';
				  
				// $message = $mrm->insertLV();
			}
			
		  }
			  $this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function updateMasterRegion() {
			
			
			$data = json_decode($this->request, true);
			
			foreach($data as $obj){
				
			$MappingId = $this->validateParameter('MappingId', $obj['MappingId'], STRING, false);
			$Title = $this->validateParameter('Title', $obj['Title'], STRING, false);
			$OrderNumber = $this->validateParameter('OrderNumber', $obj['OrderNumber'], INTEGER, false);
			
			
			$mrm = new LookupValuesModel;
			$mrm->setSysId($_GET['SysId']);
			$mrm->setMappingId($MappingId);
			$mrm->setTitle($Title);
			$mrm->setOrderNumber($OrderNumber);
			$mrm->setLastModified(date('Y-m-d'));
			$mrm->setModifier($this->userId);
			
			
			if(!$mrm->updateLV()) {
				$message = 'Failed to update.';
			} else {
				$message = "Update successfully.";
			}
			
		  }
			$this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function getMasterRegionDetails() {
			
			if(!isset($_GET['Title']) || $_GET['Title'] == "") {
				
				$masterregion = new MasterRegionModel;
				$masterregion = $masterregion->getAllMasterRegion();
				$this->returnResponseGetAll($masterregion);				
				
			}else{
			
				$Title = $this->validateParameter('Title', $_GET['Title'], STRING);
			
				$masterregion = new MasterRegionModel;
				$masterregion->setUserID($Title);
				
				$masterregion = $masterregion->getMasterRegionByTitle();
				if(!is_array($masterregion)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $masterregion);
			}
			
		}
	}
	
 ?>