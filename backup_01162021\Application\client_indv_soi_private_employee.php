<?php 
include_once '../Lib/DbConnect.php';

	class client_indv_soi_private_employee {
		
		
		private $tableName = 'client_indv_soi_private_employee';
		private $dbConn;
		
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $ParentId;
		private $Occupation;
		private $NatureOfWork;
		private $Position;
		private $Tenure;
		private $NetIncome;
		private $ContactPerson;
		private $ContactNumber;
		private $CompanyName;
		private $BldgNumber;
		private $Street;
		private $Subdivision;
		private $Barangay;
		private $City;
		private $Province;
		private $EmploymentStatus;
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setParentId($ParentId) { $this->ParentId = $ParentId; }
		function getParentId() { return $this->ParentId; }
		
		function setOccupation($Occupation) { $this->Occupation = $Occupation; }
		function getOccupation() { return $this->Occupation; }
		
		function setNatureOfWork($NatureOfWork) { $this->NatureOfWork = $NatureOfWork; }
		function getNatureOfWork() { return $this->NatureOfWork; }
		
		function setPosition($Position) { $this->Position = $Position; }
		function getPosition() { return $this->Position; }
		
		function setTenure($Tenure) { $this->Tenure = $Tenure; }
		function getTenure() { return $this->Tenure; }
		
		function setNetIncome($NetIncome) { $this->NetIncome = $NetIncome; }
		function getNetIncome() { return $this->NetIncome; }
		
		function setContactPerson($ContactPerson) { $this->ContactPerson = $ContactPerson; }
		function getContactPerson() { return $this->ContactPerson; }
		
		function setContactNumber($ContactNumber) { $this->ContactNumber = $ContactNumber; }
		function getContactNumber() { return $this->ContactNumber; }
		
		function setCompanyName($CompanyName) { $this->CompanyName = $CompanyName; }
		function getCompanyName() { return $this->CompanyName; }
		
		function setBldgNumber($BldgNumber) { $this->BldgNumber = $BldgNumber; }
		function getBldgNumber() { return $this->BldgNumber; }
		
		function setStreet($Street) { $this->Street = $Street; }
		function getStreet() { return $this->Street; }
		
		function setSubdivision($Subdivision) { $this->Subdivision = $Subdivision; }
		function getSubdivision() { return $this->Subdivision; }
		
		function setBarangay($Barangay) { $this->Barangay = $Barangay; }
		function getBarangay() { return $this->Barangay; }
		
		function setCity($City) { $this->City = $City; }
		function getCity() { return $this->City; }
		
		function setProvince($Province) { $this->Province = $Province; }
		function getProvince() { return $this->Province; }
		
		function setEmploymentStatus($EmploymentStatus) { $this->EmploymentStatus = $EmploymentStatus; }
		function getEmploymentStatus() { return $this->EmploymentStatus; }
		

		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
				
		public function client_indv_soi_private_employee() {
			
			$sql = 'INSERT INTO  client_indv_soi_private_employee (Creator,Created,ParentId,Occupation,NatureOfWork,Position,Tenure,NetIncome,ContactPerson,ContactNumber,CompanyName,BldgNumber,Street,Subdivision,Barangay,City,Province,EmploymentStatus) 
														   VALUES (:Creator,:Created,:ParentId,:Occupation,:NatureOfWork,:Position,:Tenure,:NetIncome,:ContactPerson,:ContactNumber,:CompanyName,:BldgNumber,:Street,:Subdivision,:Barangay,:City,:Province,:EmploymentStatus)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':ParentId', $this->ParentId);
			$stmt->bindParam(':Occupation', $this->Occupation);
			$stmt->bindParam(':NatureOfWork', $this->NatureOfWork);
			$stmt->bindParam(':Position', $this->Position);
			$stmt->bindParam(':Tenure', $this->Tenure);
			$stmt->bindParam(':NetIncome', $this->NetIncome);
			$stmt->bindParam(':ContactPerson', $this->ContactPerson);
			$stmt->bindParam(':ContactNumber', $this->ContactNumber);
			$stmt->bindParam(':CompanyName', $this->CompanyName);
			$stmt->bindParam(':BldgNumber', $this->BldgNumber);
			$stmt->bindParam(':Street', $this->Street);
			$stmt->bindParam(':Subdivision', $this->Subdivision);
			$stmt->bindParam(':Barangay', $this->Barangay);
			$stmt->bindParam(':City', $this->City);
			$stmt->bindParam(':Province', $this->Province);
			$stmt->bindParam(':EmploymentStatus', $this->EmploymentStatus);
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>