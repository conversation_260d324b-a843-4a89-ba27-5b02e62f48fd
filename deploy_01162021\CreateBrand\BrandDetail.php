<?php 
	include_once '../Lib/jwt.php';
	include_once 'restBrandDetail.php';
	
	class BrandDetail extends RestBrand {
		
		public function __construct() {
			parent::__construct();
		}

		
		
		public function getBrandDetails() {
			
			if(!isset($_GET['Title']) || $_GET['Title'] == "") {
				
				$BrandModel = new BrandModel;
				$BrandModel = $BrandModel->getAllBrand();
				$this->returnResponseGetAll($BrandModel);				
				
			}else{
			
				$Title = $this->validateParameter('Title', $_GET['Title'], STRING);
			
				$BrandModel = new BrandModel;
				$BrandModel->setTitle($Title);
				
				$BrandModel = $BrandModel->getBrandByTitle();
				if(!is_array($BrandModel)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $BrandModel);
			}
			
		}
	}
	
 ?>