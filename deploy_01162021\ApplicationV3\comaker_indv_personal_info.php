<?php 
include_once '../Lib/DbConnect.php';

	class comaker_indv_personal_info {
		
		
		private $tableName = 'comaker_indv_personal_info';
		private $dbConn;
			
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $ClientId;
		private $FirstName;
		private $MiddleName;
		private $LastName;
		private $SuffixName;
		private $MobileNumber;
		private $Occupation;
	
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setClientId($ClientId) { $this->ClientId = $ClientId; }
		function getClientId() { return $this->ClientId; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setFirstName($FirstName) { $this->FirstName = $FirstName; }
		function getFirstName() { return $this->FirstName; }
		
		function setMiddleName($MiddleName) { $this->MiddleName = $MiddleName; }
		function getMiddleName() { return $this->MiddleName; }
		
		function setLastName($LastName) { $this->LastName = $LastName; }
		function getLastName() { return $this->LastName; }
		
		function setSuffixName($SuffixName) { $this->SuffixName = $SuffixName; }
		function getSuffixName() { return $this->SuffixName; }
		
		function setMobileNumber($MobileNumber) { $this->MobileNumber = $MobileNumber; }
		function getMobileNumber() { return $this->MobileNumber; }
	
		function setOccupation($Occupation) { $this->Occupation = $Occupation; }
		function getOccupation() { return $this->Occupation; }
		

		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
				
		public function comaker_indv_personal_info() {
			
			$sql = 'INSERT INTO  comaker_indv_personal_info (Creator,Created,ClientId,FirstName,MiddleName,LastName,SuffixName,MobileNumber,Occupation) 
													VALUES (:Creator,:Created,:ClientId,:FirstName,:MiddleName,:LastName,:SuffixName,:MobileNumber,:Occupation)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':ClientId', $this->ClientId);
			$stmt->bindParam(':FirstName', $this->FirstName);
			$stmt->bindParam(':MiddleName', $this->MiddleName);
			$stmt->bindParam(':LastName', $this->LastName);
			$stmt->bindParam(':SuffixName', $this->SuffixName);
			$stmt->bindParam(':MobileNumber', $this->MobileNumber);
			$stmt->bindParam(':Occupation', $this->Occupation);
			
			if($stmt->execute()) {
				
			} else {
			
			}
		}
	}
 ?>