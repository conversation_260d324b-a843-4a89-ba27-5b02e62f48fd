<?php 
include_once '../Lib/DbConnect.php';

	class client_indv_soi_business {
		
		
		private $tableName = 'client_indv_soi_business';
		private $dbConn;
		
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $ParentId;
		private $Occupation;
		private $Type;
		private $Nature;
		private $YearsOfOperation;
		private $NetIncome;
		private $ContactPerson;
		private $ContactNumber;
		private $Name;
		private $BldgNumber;
		private $Street;
		private $Subdivision;
		private $Barangay;
		private $City;
		private $Province;
		private $dateOfbusinessStarted;
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setParentId($ParentId) { $this->ParentId = $ParentId; }
		function getParentId() { return $this->ParentId; }
		
		function setOccupation($Occupation) { $this->Occupation = $Occupation; }
		function getOccupation() { return $this->Occupation; }
		
		function setType($Type) { $this->Type = $Type; }
		function getType() { return $this->Type; }
		
		function setNature($Nature) { $this->Nature = $Nature; }
		function getNature() { return $this->Nature; }
		
		function setYearsOfOperation($YearsOfOperation) { $this->YearsOfOperation = $YearsOfOperation; }
		function getYearsOfOperation() { return $this->YearsOfOperation; }
		
		function setNetIncome($NetIncome) { $this->NetIncome = $NetIncome; }
		function getNetIncome() { return $this->NetIncome; }
		
		function setContactPerson($ContactPerson) { $this->ContactPerson = $ContactPerson; }
		function getContactPerson() { return $this->ContactPerson; }
		
		function setContactNumber($ContactNumber) { $this->ContactNumber = $ContactNumber; }
		function getContactNumber() { return $this->ContactNumber; }
		
		function setName($Name) { $this->Name = $Name; }
		function getName() { return $this->Name; }
		
		function setBldgNumber($BldgNumber) { $this->BldgNumber = $BldgNumber; }
		function getBldgNumber() { return $this->BldgNumber; }
		
		function setStreet($Street) { $this->Street = $Street; }
		function getStreet() { return $this->Street; }
		
		function setSubdivision($Subdivision) { $this->Subdivision = $Subdivision; }
		function getSubdivision() { return $this->Subdivision; }
		
		function setBarangay($Barangay) { $this->Barangay = $Barangay; }
		function getBarangay() { return $this->Barangay; }
		
		function setCity($City) { $this->City = $City; }
		function getCity() { return $this->City; }
		
		function setProvince($Province) { $this->Province = $Province; }
		function getProvince() { return $this->Province; }
		
		function setDateOfbusinessStarted($DateOfbusinessStarted) { $this->DateOfbusinessStarted = $DateOfbusinessStarted; }
		function getDateOfbusinessStarted() { return $this->DateOfbusinessStarted; }
		

		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
				
		public function client_indv_soi_business() {
			
			$sql = 'INSERT INTO  client_indv_soi_business (Creator,Created,ParentId,Occupation,Type,Nature,YearsOfOperation,NetIncome,ContactPerson,ContactNumber,Name,BldgNumber,Street,Subdivision,Barangay,City,Province,dateOfbusinessStarted) 
												   VALUES (:Creator,:Created,:ParentId,:Occupation,:Type,:Nature,:YearsOfOperation,:NetIncome,:ContactPerson,:ContactNumber,:Name,:BldgNumber,:Street,:Subdivision,:Barangay,:City,:Province,:DateOfbusinessStarted)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':ParentId', $this->ParentId);
			$stmt->bindParam(':Occupation', $this->Occupation);
			$stmt->bindParam(':Type', $this->Type);
			$stmt->bindParam(':Nature', $this->Nature);
			$stmt->bindParam(':YearsOfOperation', $this->YearsOfOperation);
			$stmt->bindParam(':NetIncome', $this->NetIncome);
			$stmt->bindParam(':ContactPerson', $this->ContactPerson);
			$stmt->bindParam(':ContactNumber', $this->ContactNumber);
			$stmt->bindParam(':Name', $this->Name);
			$stmt->bindParam(':BldgNumber', $this->BldgNumber);
			$stmt->bindParam(':Street', $this->Street);
			$stmt->bindParam(':Subdivision', $this->Subdivision);
			$stmt->bindParam(':Barangay', $this->Barangay);
			$stmt->bindParam(':City', $this->City);
			$stmt->bindParam(':Province', $this->Province);
			$stmt->bindParam(':DateOfbusinessStarted', $this->DateOfbusinessStarted);
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>