<?php 
include_once '../Lib/DbConnect.php';

	class Master_branchModel {
		
		public $tableName = 'Master_branch';
		public $dbConn;
	
		public $SysId;
		public $Created;
		public $Creator;
		public $LastModified;
		public $Modifier;
		public $Type;
		public $Title;
		public $ParentCompanyCode;
		public $CompanyCode;
		public $DealerGroup;
		public $City;
		public $Province;
		public $Barangay;
		public $Street;
		public $Building;
		public $Longitude;
		public $Latitude;
		public $Cluster;
		public $Mnemonic;
		public $CIProvider;
		
	
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setType($Type) { $this->Type = $Type; }
		function getType() { return $this->Type; }
		
		function setTitle($Title) { $this->Title = $Title; }
		function getTitle() { return $this->Title; }
		
		function setParentCompanyCode($ParentCompanyCode) { $this->ParentCompanyCode = $ParentCompanyCode; }
		function getParentCompanyCode() { return $this->ParentCompanyCode; }
				
		function setCompanyCode($CompanyCode) { $this->CompanyCode = $CompanyCode; }
		function getCompanyCode() { return $this->CompanyCode; }
	
		function setDealerGroup($DealerGroup) { $this->DealerGroup = $DealerGroup; }
		function getDealerGroup() { return $this->DealerGroup; }
	
		function setCity($City) { $this->City = $City; }
		function getCity() { return $this->City; }
	
		function setProvince($Province) { $this->Province = $Province; }
		function getProvince() { return $this->Province; }
	
		function setBarangay($Barangay) { $this->Barangay = $Barangay; }
		function getBarangay() { return $this->Barangay; }
	
		function setStreet($Street) { $this->Street = $Street; }
		function getStreet() { return $this->Street; }
	
		function setBuilding($Building) { $this->Building = $Building; }
		function getBuilding() { return $this->Building; }
	
		function setLongitude($Longitude) { $this->Longitude = $Longitude; }
		function getLongitude() { return $this->Longitude; }
	
		function setLatitude($Latitude) { $this->Latitude = $Latitude; }
		function getLatitude() { return $this->Latitude; }
	
		function setCluster($Cluster) { $this->Cluster = $Cluster; }
		function getCluster() { return $this->Cluster; }
	
		function setMnemonic($Mnemonic) { $this->Mnemonic = $Mnemonic; }
		function getMnemonic() { return $this->Mnemonic; }
	
		function setCIProvider($CIProvider) { $this->CIProvider = $CIProvider; }
		function getCIProvider() { return $this->CIProvider; }
	
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		public function getMaster_branchByTitle() {
					
					
					$sql = "select * from Master_branch a where a.Title = :Title";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':Title', $this->Title);
					$stmt->execute();
					$Master_branch = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $Master_branch;
					
		}
		
		public function getAllMaster_branch() {
					
					$sql = "select * from Master_branch";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->execute();
					$Master_branch = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $Master_branch;
		}
	}
 ?>