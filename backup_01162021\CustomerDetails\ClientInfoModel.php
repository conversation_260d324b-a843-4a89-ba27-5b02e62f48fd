<?php 
include_once '../Lib/DbConnect.php';
	class ClientInfoModel {
		
		private $tableName = 'client_indv_personal_info';
		private $dbConn;
		
		private $SysId;
		private $FirstName;
		private $MiddleName;
		private $LastName;
		private $MobileNumber;
		private $SuffixName;
		private $DOB;
		private $Nationality;
		private $Religion;
		private $ApplicationID;
		private $CivilStatus;
		private $Gender;
		private $Email;
		private $Created;
		private $Creator;
		private $Source;
		private $BirthPlace;
		private $Employer;
		private $Position;
		private $Branch;
		private $PerAddBlkNumber;
		private $PerAddStreet;
		private $PerAddBarangay;
		private $PerAddCity;
		private $PerAddProvince;
		private $PerAddZipCode;	
		private $CurrAddBlkNumber;
		private $CurrAddStreet;
		private $CurrAddBarangay;
		private $CurrAddCity;
		private $CurrAddProvince;
		private $CurrAddZipCode;
		

		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setFirstName($FirstName) { $this->FirstName = $FirstName; }
		function getFirstName() { return $this->FirstName; }
		
		function setMiddleName($MiddleName) { $this->MiddleName = $MiddleName; }
		function getMiddleName() { return $this->MiddleName; }
		
		function setLastName($LastName) { $this->LastName = $LastName; }
		function getLastName() { return $this->LastName; }
		
		function setSuffixName($SuffixName) { $this->SuffixName = $SuffixName; }
		function getSuffixName() { return $this->SuffixName; }
		
		function setDOB($DOB) { $this->DOB = $DOB; }
		function getDOB() { return $this->DOB; }
		
		function setNationality($Nationality) { $this->Nationality = $Nationality; }
		function getNationality() { return $this->Nationality; }
		
		function setReligion($Religion) { $this->Religion = $Religion; }
		function getReligion() { return $this->Religion; }
		
		function setApplicationID($ApplicationID) { $this->ApplicationID = $ApplicationID; }
		function getApplicationID() { return $this->ApplicationID; }
		
		function setCivilStatus($CivilStatus) { $this->CivilStatus = $CivilStatus; }
		function getCivilStatus() { return $this->CivilStatus; }
		
		function setGender($Gender) { $this->Gender = $Gender; }
		function getGender() { return $this->Gender; }
		
		function setEmail($email) { $this->email = $email; }
		function getEmail() { return $this->email; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setMobileNumber($MobileNumber) { $this->MobileNumber = $MobileNumber; }
		function getMobileNumber() { return $this->MobileNumber; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setSource($Source) { $this->Source = $Source; }
		function getSource() { return $this->Source; }
		
		function setBirthPlace($BirthPlace) { $this->BirthPlace = $BirthPlace; }
		function getBirthPlace() { return $this->BirthPlace; }
		
		function setEmployer($Employer) { $this->Employer = $Employer; }
		function getEmployer() { return $this->Employer; }

		function setPosition($Position) { $this->Position = $Position; }
		function getPosition() { return $this->Position; }

		function setBranch($Branch) { $this->Branch = $Branch; }
		function getBranch() { return $this->Branch; }

		function setPerAddBlkNumber($PerAddBlkNumber) { $this->PerAddBlkNumber = $PerAddBlkNumber; }
		function getPerAddBlkNumber() { return $this->PerAddBlkNumber; }

		function setPerAddStreet($PerAddStreet) { $this->PerAddStreet = $PerAddStreet; }
		function getPerAddStreet() { return $this->PerAddStreet; }

		function setPerAddBarangay($PerAddBarangay) { $this->PerAddBarangay = $PerAddBarangay; }
		function getPerAddBarangay() { return $this->PerAddBarangay; }

		function setPerAddCity($PerAddCity) { $this->PerAddCity = $PerAddCity; }
		function getPerAddCity() { return $this->PerAddCity; }

		function setPerAddProvince($PerAddProvince) { $this->PerAddProvince = $PerAddProvince; }
		function getPerAddProvince() { return $this->PerAddProvince; }

		function setPerAddZipCode($PerAddZipCode) { $this->PerAddZipCode = $PerAddZipCode; }
		function getPerAddZipCode() { return $this->PerAddZipCode; }

		function setCurrAddBlkNumber($CurrAddBlkNumber) { $this->CurrAddBlkNumber = $CurrAddBlkNumber; }
		function getCurrAddBlkNumber() { return $this->CurrAddBlkNumber; }

		function setCurrAddStreet($CurrAddStreet) { $this->CurrAddStreet = $CurrAddStreet; }
		function getCurrAddStreet() { return $this->CurrAddStreet; }

		function setCurrAddBarangay($CurrAddBarangay) { $this->CurrAddBarangay = $CurrAddBarangay; }
		function getCurrAddBarangay() { return $this->CurrAddBarangay; }

		function setCurrAddCity($CurrAddCity) { $this->CurrAddCity = $CurrAddCity; }
		function getCurrAddCity() { return $this->CurrAddCity; }

		function setCurrAddProvince($CurrAddProvince) { $this->CurrAddProvince = $CurrAddProvince; }
		function getCurrAddProvince() { return $this->CurrAddProvince; }

		function setCurrAddZipCode($CurrAddZipCode) { $this->CurrAddZipCode = $CurrAddZipCode; }
		function getCurrAddZipCode() { return $this->CurrAddZipCode; }

		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		public function getCustomerDetailsById() {
					
					
					$sql = "select a.* from client_indv_personal_info a, user_account b
									where a.Creator = b.SysId
									and a.SysId =:SysId";

					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':SysId', $this->SysId);
					$stmt->execute();
					$customer = $stmt->fetch(PDO::FETCH_ASSOC);
					
					return $customer;
					
		}
		
		public function getAllCustomer() {
					
					$sql = "select a.* from client_indv_personal_info a, user_account b where a.Creator = b.SysId";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->execute();
					$customer = $stmt->fetchAll(PDO::FETCH_ASSOC);
					
				
					
					return $customer;
		}
				
		public function insert() {
			
			$sql = 'INSERT INTO ' . $this->tableName . '(FirstName,MiddleName,LastName,MobileNumber,SuffixName,DOB,Nationality,Religion,ApplicationID,CivilStatus,Gender,Email,Created,Creator,Source,BirthPlace,Employer,Position,Branch,PerAddBlkNumber,PerAddStreet,PerAddBarangay,PerAddCity,PerAddProvince,PerAddZipCode,CurrAddBlkNumber,CurrAddStreet,CurrAddBarangay,CurrAddCity,CurrAddProvince,CurrAddZipCode) 
														VALUES(:FirstName,:MiddleName,:LastName,:MobileNumber,:SuffixName,:DOB,:Nationality,:Religion,:ApplicationID,:CivilStatus,:Gender,:Email,:Created,:Creator,:Source,:BirthPlace,:Employer,:Position,:Branch,:PerAddBlkNumber,:PerAddStreet,:PerAddBarangay,:PerAddCity,:PerAddProvince,:PerAddZipCode,:CurrAddBlkNumber,:CurrAddStreet,:CurrAddBarangay,:CurrAddCity,:CurrAddProvince,:CurrAddZipCode)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':FirstName', $this->FirstName);
			$stmt->bindParam(':MiddleName', $this->MiddleName);
			$stmt->bindParam(':LastName', $this->LastName);
			$stmt->bindParam(':MobileNumber', $this->MobileNumber);
			$stmt->bindParam(':SuffixName', $this->SuffixName);
			$stmt->bindParam(':DOB', $this->DOB);
			$stmt->bindParam(':Nationality', $this->Nationality);
			$stmt->bindParam(':Religion', $this->Religion);
			$stmt->bindParam(':ApplicationID', $this->ApplicationID);
			$stmt->bindParam(':CivilStatus', $this->CivilStatus);
			$stmt->bindParam(':Gender', $this->Gender);
			$stmt->bindParam(':Email', $this->Email);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Source', $this->Source);
			$stmt->bindParam(':BirthPlace', $this->BirthPlace);
			$stmt->bindParam(':Employer', $this->Employer);
			$stmt->bindParam(':Position', $this->Position);
			$stmt->bindParam(':Branch', $this->Branch);
			$stmt->bindParam(':PerAddBlkNumber', $this->PerAddBlkNumber);
			$stmt->bindParam(':PerAddStreet', $this->PerAddStreet);
			$stmt->bindParam(':PerAddBarangay', $this->PerAddBarangay);
			$stmt->bindParam(':PerAddCity', $this->PerAddCity);
			$stmt->bindParam(':PerAddProvince', $this->PerAddProvince);
			$stmt->bindParam(':PerAddZipCode', $this->PerAddZipCode);
			$stmt->bindParam(':CurrAddBlkNumber', $this->CurrAddBlkNumber);
			$stmt->bindParam(':CurrAddStreet', $this->CurrAddStreet);
			$stmt->bindParam(':CurrAddBarangay', $this->CurrAddBarangay);
			$stmt->bindParam(':CurrAddCity', $this->CurrAddCity);
			$stmt->bindParam(':CurrAddProvince', $this->CurrAddProvince);
			$stmt->bindParam(':CurrAddZipCode', $this->CurrAddZipCode);
			
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>