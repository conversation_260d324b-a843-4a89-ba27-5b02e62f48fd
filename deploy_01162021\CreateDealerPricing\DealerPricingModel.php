<?php 
include_once '../Lib/DbConnect.php';

	class DealerPricingModel {
		
		public $tableName = 'DealerPricing';
		public $dbConn;
		
		public $SysId;
		public $Created;
		public $Creator;
		public $LastModified;
		public $Modifier;
		public $DealerKey;
		public $DealerBranchKey;
		public $AreaCode;
		public $Brand;
		public $Model;
		public $Price;
		public $AsOf;
		public $IsActive;
		public $DPFixedAmount;
		

		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setDealerKey($DealerKey) { $this->DealerKey = $DealerKey; }
		function getDealerKey() { return $this->DealerKey; }
		
		function setDealerBranchKey($DealerBranchKey) { $this->DealerBranchKey = $DealerBranchKey; }
		function getDealerBranchKey() { return $this->DealerBranchKey; }
		
		function setAreaCode($AreaCode) { $this->AreaCode = $AreaCode; }
		function getAreaCode() { return $this->AreaCode; }
				
		function setCode($Code) { $this->Code = $Code; }
		function getCode() { return $this->Code; }
	
		function setBrand($Brand) { $this->Brand = $Brand; }
		function getBrand() { return $this->Brand; }
	
		function setModel($Model) { $this->Model = $Model; }
		function getModel() { return $this->Model; }
	
		function setPrice($Price) { $this->Price = $Price; }
		function getPrice() { return $this->Price; }
	
		function setAsOf($AsOf) { $this->AsOf = $AsOf; }
		function getAsOf() { return $this->AsOf; }
	
		function setIsActive($IsActive) { $this->IsActive = $IsActive; }
		function getIsActive() { return $this->IsActive; }
	
		function setDPFixedAmount($DPFixedAmount) { $this->DPFixedAmount = $DPFixedAmount; }
		function getDPFixedAmount() { return $this->DPFixedAmount; }
	
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		public function getDealerPricingByDealerBranchKey() {
					
					
					$sql = "select * from DealerPricing a where a.DealerBranchKey = :DealerBranchKey";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':DealerBranchKey', $this->DealerBranchKey);
					$stmt->execute();
					$DealerPricing = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $DealerPricing;
					
		}
		
		public function getAllDealerPricing() {
					
					$sql = "select * from DealerPricing";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->execute();
					$DealerPricing = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $DealerPricing;
		}
	}
 ?>