<?php 
	include_once '../Lib/jwt.php';
	include_once 'restCIResultDetail.php';
	
	class CIResultDetail extends RestCIResult {
		
		public function __construct() {
			parent::__construct();
		}
		
		public function updateftvjobstarted() {
			
			$data = json_decode($this->request, true);
			
						$ftv_job_model = new ftv_job_model;
						$ftv_job_model->setDateTimeStarted(date('Y-m-d H:i:s'));
						$ftv_job_model->setStatus("JOB STARTED");
						$ftv_job_model->setSysId($_GET['jobId']);
						$ftv_job_model->setLastModified(date('Y-m-d H:i:s'));
						$ftv_job_model->setModifier($this->userId);
					
									
					if(!$ftv_job_model->updateftvjobstarted()) {
						$message = 'Failed to update.';
					} else {
						$message = "Update successfully.";
					}			
			$this->returnResponse(SUCCESS_RESPONSE, $message);
			
		}
		
		public function Validate_data(){
			
			$data = json_decode($this->request, true);
			
				foreach($data as $objheader){	
					
					$JobId = $this->validateParameter('JobId', $objheader['jobId'], STRING);
					$FullName = $this->validateParameter('FullName', $objheader['fullName'], STRING);
					$Relationship = $this->validateParameter('Relationship', $objheader['relationship'], STRING);
					$DoneFTV_Lat = $this->validateParameter('DoneFTV_Lat', $objheader['doneFTV_Lat'], STRING);
					$DoneFTV_Lon = $this->validateParameter('DoneFTV_Lon', $objheader['doneFTV_Lon'], STRING);
					
							//ci result
							$result = $objheader['result'];
							foreach($result as $objchildresult){
								
								$Question = $this->validateParameter('Question', $objchildresult['question'], STRING);
								$Answer = $this->validateParameter('Answer', $objchildresult['answer'], STRING, false);
								$OrderNumber = $this->validateParameter('OrderNumber', $objchildresult['orderNumber'], INTEGER);
								$JobID = $this->validateParameter('JobID', $objchildresult['jobID'], STRING);
								
									$ftv_job_result_qna_model = new ftv_job_result_qna_model;
									$ftv_job_result_qna_model->setJobId($JobId);
									$ftv_job_result_qna_model->setQuestion($Question);
									$ftv_job_result_qna_model->setAnswer($Answer);
									$ftv_job_result_qna_model->setOrderNumber($OrderNumber);
									$ftv_job_result_qna_model->setCreated(date('Y-m-d H:i:s'));
									$ftv_job_result_qna_model->setCreator($this->userId);
									
									if(!$ftv_job_result_qna_model->ftv_job_result_q_n_aa()) {
									} else {
									}
						
								}
							
							
							
							//ci attachment
							$attachment = $objheader['attachment'];
							foreach($attachment as $objchildattachment){
								
								$Category = $this->validateParameter('Category', $objchildattachment['category'], STRING);
								$Remarks = $this->validateParameter('Remarks', $objchildattachment['remarks'], STRING, FALSE);
								$ProofImage = $this->validateParameter('ProofImage', $objchildattachment['proofImage'], STRING);
								$JobID = $this->validateParameter('JobID', $objchildattachment['jobId'], STRING);
								
									$ftv_job_attachment_model = new ftv_job_attachment_model;
									$ftv_job_attachment_model->setJobId($JobId);
									$ftv_job_attachment_model->setCategory($Category);
									$ftv_job_attachment_model->setRemarks($Remarks);
									$ftv_job_attachment_model->setProofImage($ProofImage);
									$ftv_job_attachment_model->setCreated(date('Y-m-d H:i:s'));
									$ftv_job_attachment_model->setCreator($this->userId);
									
									if(!$ftv_job_attachment_model->ftv_job_attachment()) {
									} else {
									}
							
							}
					
						$ftv_job_result_model = new ftv_job_result_model;
						$ftv_job_result_model->setJobId($JobId);
						$ftv_job_result_model->setFullName($FullName);
						$ftv_job_result_model->setRelationship($Relationship);
						$ftv_job_result_model->setLastModified(date('Y-m-d H:i:s'));
						$ftv_job_result_model->setModifier($this->userId);
						$ftv_job_result_model->setCreated(date('Y-m-d H:i:s'));
						$ftv_job_result_model->setCreator($this->userId);
						
						if(!$ftv_job_result_model->ftv_job_result()) {
						} else {
						}
						
						$ftv_job_model = new ftv_job_model;
						$ftv_job_model->setDateTimeCompleted(date('Y-m-d H:i:s'));
						$ftv_job_model->setDoneFTV_Lat($DoneFTV_Lat);
						$ftv_job_model->setDoneFTV_Lon($DoneFTV_Lon);
						$ftv_job_model->setStatus("JOB COMPLETED");
						$ftv_job_model->setSysId($JobId);
						$ftv_job_model->setLastModified(date('Y-m-d H:i:s'));
						$ftv_job_model->setModifier($this->userId);
					
									
						if(!$ftv_job_model->updateftvjob()) {
						} else {
						}

						$ftv_job_model = new ftv_job_model;
						$ftv_job_model->setSysId($JobId);
						
									
					if(!$ftv_job_model->Getpostcistatus()) {
						$message = 'Failed to update.';
					} else {
						//$message = "Update successfully.".$ftv_job_model->Getpostcistatus();
						//$message = "Update successfully.";
					}							
						
			}
			
			//$this->returnResponse(SUCCESS_RESPONSE, $message);
		}			
	}
	
 ?>