<?php 
include_once '../Lib/DbConnect.php';

	class MasterProvinceModel {
		
		public $tableName = 'master_province';
		public $dbConn;
		
		
		public $SysId;
		public $Created;
		public $Creator;
		public $LastModified;
		public $Modifier;
		public $MappingId;
		public $Title;
		public $OrderNumber;
		public $ParentId;
	
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setMappingId($MappingId) { $this->MappingId = $MappingId; }
		function getMappingId() { return $this->MappingId; }
		
		
		function setTitle($Title) { $this->Title = $Title; }
		function getTitle() { return $this->Title; }
		
		function setOrderNumber($OrderNumber) { $this->OrderNumber = $OrderNumber; }
		function getOrderNumber() { return $this->OrderNumber; }
		
		function setParentId($ParentId) { $this->ParentId = $ParentId; }
		function getParentId() { return $this->ParentId; }
		
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		public function getMasterProvinceByTitle() {
					
					
					$sql = "select * from master_province a where a.Title = :Title";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':Title', $this->Title);
					$stmt->execute();
					$masterregion = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $masterregion;
					
		}
		
		public function getAllMasterProvince() {
					
					$sql = "select * from master_province order by OrderNumber ";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->execute();
					$masterregion = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $masterregion;
		}
		
		public function updatePM() {
			
			$sql = 'update master_province set LastModified=:LastModified,Modifier=:Modifier,MappingId=:MappingId,
													Title=:Title,OrderNumber=:OrderNumber,Category=:Category,
													IsActive=:IsActive,ParentId=:ParentId where SysId =:SysId';
			
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':LastModified', $this->LastModified);
			$stmt->bindParam(':Modifier', $this->Modifier);
			$stmt->bindParam(':MappingId', $this->MappingId);;
			$stmt->bindParam(':Title', $this->Title);
			$stmt->bindParam(':OrderNumber', $this->OrderNumber);
			$stmt->bindParam(':ParentId', $this->ParentId);
			$stmt->bindParam(':SysId', $this->SysId);
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
				
		public function insertPM() {
			

			$sql = 'INSERT INTO  master_province (Creator,Created,MappingId,Title,OrderNumber,ParentId) 
												VALUES(:Creator,:Created,:MappingId,:Title,:ParentId,:OrderNumber)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':MappingId', $this->MappingId);
			$stmt->bindParam(':Title', $this->Title);
			$stmt->bindParam(':ParentId', $this->ParentId);
			$stmt->bindParam(':OrderNumber', $this->OrderNumber);
			
			if($stmt->execute()) {				 
			
				 // $SysId = $this->dbConn->lastInsertId();
				 // return $SysId;
				 
				return true;
				
			} else {
				return false;
			}
		}
	}
 ?>