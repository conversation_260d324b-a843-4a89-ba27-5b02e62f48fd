<?php 
include_once '../Lib/DbConnect.php';

	class SignatureModel {
		
		
		public $ApplicationId;
	
		function setApplicationId($ApplicationId) { $this->ApplicationId = $ApplicationId; }
		function getApplicationId() { return $this->ApplicationId; }
		
		
		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}

		public function getSignatureByTitle() {
					
					
					$sql = "select b.SysId,ApplicationID,ApplicationDate,b.FirstName,b.MiddleName,b.LastName,b.SuffixName,c.ImageBase64 from 
							Mc_loan_application a

							left join client_indv_personal_info b
							on a.ClientInfoId = b.SysId

							left join Mc_loan_attachment c
							on b.SysId = c.ParentId
							
							where ApplicationID =:ApplicationID; ";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->bindParam(':ApplicationID', $this->ApplicationID);
					$stmt->execute();
					$Signature = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $Signature;
					
		}
		
		public function getAllSignatures() {
					
					$sql = "SELECT A.SysId,B.Description
							FROM tblinvcolor A
							LEFT JOIN tblcolor B ON A.ColorId=B.ColorId
							LEFT JOIN DealerPricing c on a.AcuInvId = c.acu_InvId
							WHERE a.AcuInvId IS NOT NULL and a.Status = 1";
					$stmt = $this->dbConn->prepare($sql);
					$stmt->execute();
					$Signature = $stmt->fetchAll(PDO::FETCH_ASSOC);
					return $Signature;
					
		}
	}
 ?>