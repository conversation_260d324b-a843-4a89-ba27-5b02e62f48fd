<?php 
	include_once '../Lib/jwt.php';
	include_once 'restMaster_branchDetail.php';
	
	class Master_branchDetail extends RestMaster_branch {
		
		public function __construct() {
			parent::__construct();
		}

		
		public function getMaster_branchDetails() {
			
			if(!isset($_GET['Title']) || $_GET['Title'] == "") {
				
				$Master_branchModel = new Master_branchModel;
				$Master_branchModel = $Master_branchModel->getAllMaster_branch();
				//$this->returnResponseGetAll($Master_branchModel);		
			$this->returnResponse(SUCCESS_RESPONSE, $Master_branchModel);				
				
			}else{
			
				$Title = $this->validateParameter('Title', $_GET['Title'], STRING);
			
				$Master_branchModel = new Master_branchModel;
				$Master_branchModel->setTitle($Title);
				
				$Master_branchModel = $Master_branchModel->getMaster_branchByTitle();
				if(!is_array($Master_branchModel)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $Master_branchModel);
			}
			
		}
	}
	
 ?>