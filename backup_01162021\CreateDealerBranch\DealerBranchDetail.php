<?php 
	include_once '../Lib/jwt.php';
	include_once 'restDealerBranchDetail.php';
	
	class DealerBranchDetail extends RestDealerBranch {
		
		public function __construct() {
			parent::__construct();
		}

		
		
		public function getDealerBranchDetails() {
			
			if(!isset($_GET['Title']) || $_GET['Title'] == "") {
				
				$DealerBranchModel = new DealerBranchModel;
				$DealerBranchModel = $DealerBranchModel->getAllDealerBranch();
				$this->returnResponseGetAll($DealerBranchModel);				
				
			}else{
			
				$Title = $this->validateParameter('Title', $_GET['Title'], STRING);
			
				$DealerBranchModel = new DealerBranchModel;
				$DealerBranchModel->setTitle($Title);
				
				$DealerBranchModel = $DealerBranchModel->getDealerBranchByTitle();
				if(!is_array($DealerBranchModel)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $DealerBranchModel);
			}
			
		}
	}
	
 ?>