<?php 
include_once '../Lib/DbConnect.php';

	class client_indv_family_expenditure {
		
		
		private $tableName = 'client_indv_family_expenditure';
		private $dbConn;
	
		
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $Food;
		private $EducAllowance;
		private $EducTuitionFeePublic;
		private $EducTuitionFeePrivate;
		private $Electricity;
		private $ClientId;
		private $Water;
		private $ELoad;
		private $CableTV;
		private $Internet;
		private $Transportation;
		private $Medical;
		private $ExistingObligation;
		private $Miscellaneous;
		private $HouseRent;
		private $Utilities;
		private $Others;
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setFood($Food) { $this->Food = $Food; }
		function getFood() { return $this->Food; }
		
		function setEducAllowance($EducAllowance) { $this->EducAllowance = $EducAllowance; }
		function getEducAllowance() { return $this->EducAllowance; }
		
		function setEducTuitionFeePublic($EducTuitionFeePublic) { $this->EducTuitionFeePublic = $EducTuitionFeePublic; }
		function getEducTuitionFeePublic() { return $this->EducTuitionFeePublic; }
		
		function setEducTuitionFeePrivate($EducTuitionFeePrivate) { $this->EducTuitionFeePrivate = $EducTuitionFeePrivate; }
		function getEducTuitionFeePrivate() { return $this->EducTuitionFeePrivate; }
		
		function setElectricity($Electricity) { $this->Electricity = $Electricity; }
		function getElectricity() { return $this->Electricity; }
		
		function setClientId($ClientId) { $this->ClientId = $ClientId; }
		function getClientId() { return $this->ClientId; }
		
		function setWater($Water) { $this->Water = $Water; }
		function getWater() { return $this->Water; }
		
		function setELoad($ELoad) { $this->ELoad = $ELoad; }
		function getELoad() { return $this->ELoad; }
		
		function setCableTV($CableTV) { $this->CableTV = $CableTV; }
		function getCableTV() { return $this->CableTV; }
		
		function setInternet($Internet) { $this->Internet = $Internet; }
		function getInternet() { return $this->Internet; }
		
		function setTransportation($Transportation) { $this->Transportation = $Transportation; }
		function getTransportation() { return $this->Transportation; }
		
		function setMedical($Medical) { $this->Medical = $Medical; }
		function getMedical() { return $this->Medical; }
		
		function setExistingObligation($ExistingObligation) { $this->ExistingObligation = $ExistingObligation; }
		function getExistingObligation() { return $this->ExistingObligation; }
		
		function setMiscellaneous($Miscellaneous) { $this->Miscellaneous = $Miscellaneous; }
		function getMiscellaneous() { return $this->Miscellaneous; }
		
		function setHouseRent($HouseRent) { $this->HouseRent = $HouseRent; }
		function getHouseRent() { return $this->HouseRent; }
		
		function setUtilities($Utilities) { $this->Utilities = $Utilities; }
		function getUtilities() { return $this->Utilities; }
		
		function setOthers($Others) { $this->Others = $Others; }
		function getOthers() { return $this->Others; }
		

		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
				
		public function client_indv_family_expenditure() {
			
			$sql = 'INSERT INTO  client_indv_family_expenditure (Creator,Created,Food,EducAllowance,EducTuitionFeePublic,EducTuitionFeePrivate,Electricity,ClientId,Water,ELoad,CableTV,Internet,Transportation,Medical,ExistingObligation,Miscellaneous,HouseRent,Utilities,Others) 
												         VALUES (:Creator,:Created,:Food,:EducAllowance,:EducTuitionFeePublic,:EducTuitionFeePrivate,:Electricity,:ClientId,:Water,:ELoad,:CableTV,:Internet,:Transportation,:Medical,:ExistingObligation,:Miscellaneous,:HouseRent,:Utilities,:Others)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':Food', $this->Food);
			$stmt->bindParam(':EducAllowance', $this->EducAllowance);
			$stmt->bindParam(':EducTuitionFeePublic', $this->EducTuitionFeePublic);
			$stmt->bindParam(':EducTuitionFeePrivate', $this->EducTuitionFeePrivate);
			$stmt->bindParam(':Electricity', $this->Electricity);
			$stmt->bindParam(':ClientId', $this->ClientId);
			$stmt->bindParam(':Water', $this->Water);
			$stmt->bindParam(':ELoad', $this->ELoad);
			$stmt->bindParam(':CableTV', $this->CableTV);
			$stmt->bindParam(':Internet', $this->Internet);
			$stmt->bindParam(':Transportation', $this->Transportation);
			$stmt->bindParam(':Medical', $this->Medical);
			$stmt->bindParam(':ExistingObligation', $this->ExistingObligation);
			$stmt->bindParam(':Miscellaneous', $this->Miscellaneous);
			$stmt->bindParam(':HouseRent', $this->HouseRent);
			$stmt->bindParam(':Utilities', $this->Utilities);
			$stmt->bindParam(':Others', $this->Others);
			
			if($stmt->execute()) {
				return true;
			} else {
				return false;
			}
		}
	}
 ?>