<?php 
include_once '../Lib/DbConnect.php';

	class client_indv_source_of_income {
		
		
		private $tableName = 'client_indv_source_of_income';
		private $dbConn;
	
		private $SysId;
		private $Created;
		private $Creator;
		private $LastModified;
		private $Modifier;
		private $ClientId;
		private $Category;
		private $Type;
		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setClientId($ClientId) { $this->ClientId = $ClientId; }
		function getClientId() { return $this->ClientId; }
		
		function setCategory($Category) { $this->Category = $Category; }
		function getCategory() { return $this->Category; }
		
		function setType($Type) { $this->Type = $Type; }
		function getType() { return $this->Type; }
		

		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
				
		public function client_indv_source_of_income() {
			
			$sql = 'INSERT INTO  client_indv_source_of_income (Creator,Created,ClientId,Category,Type) 
												       VALUES (:Creator,:Created,:ClientId,:Category,:Type)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':ClientId', $this->ClientId);
			$stmt->bindParam(':Category', $this->Category);
			$stmt->bindParam(':Type', $this->Type);
			
			if($stmt->execute()) {
				
				$SysId = $this->dbConn->lastInsertId();
				return $SysId;
				
			} else {
				
				$SysId = null;
				return $SysId;
			}
			
		}
	}
 ?>