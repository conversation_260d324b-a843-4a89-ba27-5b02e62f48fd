<?php 
	include_once '../Lib/jwt.php';
	include_once 'restSignatureDetail.php';
	
	class SignatureDetail extends RestSignature {
		
		public function __construct() {
			parent::__construct();
		}

		
		
		public function getSignatureDetails() {
			
			if(!isset($_GET['ApplicationID']) || $_GET['ApplicationID'] == "") {
				
				$SignatureModel = new SignatureModel;
				$SignatureModel = $SignatureModel->getAllSignature();
				$this->returnResponseGetAll($SignatureModel);		
				//$this->returnResponse(SUCCESS_RESPONSE, $SignatureModel);				
				
			}else{
			
				$ApplicationID = $this->validateParameter('ApplicationID', $_GET['ApplicationID'], STRING);
			
				$SignatureModel = new SignatureModel;
				$SignatureModel->setApplicationID($ApplicationID);
				
				$SignatureModel = $SignatureModel->getSignatureByTitle();
				if(!is_array($SignatureModel)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $SignatureModel);
			}
			
		}
	}
	
 ?>