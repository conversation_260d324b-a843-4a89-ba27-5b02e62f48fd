<?php 
	include_once '../Lib/jwt.php';
	include_once 'restDealerAccountDetail.php';
	
	class DealerAccountDetail extends RestDealerAccount {
		
		public function __construct() {
			parent::__construct();
		}

		
		
		public function getDealerAccountDetails() {
			
			if(!isset($_GET['Title']) || $_GET['Title'] == "") {
				
				$DealerAccountModel = new DealerAccountModel;
				$DealerAccountModel = $DealerAccountModel->getAllDealerAccount();
				$this->returnResponseGetAll($DealerAccountModel);		
				//$this->returnResponse(SUCCESS_RESPONSE, $DealerAccountModel);				
				
			}else{
			
				$Title = $this->validateParameter('Title', $_GET['Title'], STRING);
			
				$DealerAccountModel = new DealerAccountModel;
				$DealerAccountModel->setTitle($Title);
				
				$DealerAccountModel = $DealerAccountModel->getDealerAccountByTitle();
				if(!is_array($DealerAccountModel)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $DealerAccountModel);
			}
			
		}
	}
	
 ?>