<?php 
include_once '../Lib/DbConnect.php';

	class CsbApplicationStatusModel {
		
		public $tableName = 'csb_application_status';
		public $dbConn;
		
		public $SysId;
		public $Created;
		public $Creator;
		public $LastModified;
		public $Modifier;
		public $Csb_ApplicationId;
		public $Csb_Application_Status;
		public $Csb_Application_Remarks;

		
		function setSysId($SysId) { $this->SysId = $SysId; }
		function getSysId() { return $this->SysId; }
		
		function setCreator($Creator) { $this->Creator = $Creator; }
		function getCreator() { return $this->Creator; }
		
		function setLastModified($LastModified) { $this->LastModified = $LastModified; }
		function getLastModified() { return $this->LastModified; }
		
		function setCreated($Created) { $this->Created = $Created; }
		function getCreated() { return $this->Created; }
		
		function setModifier($Modifier) { $this->Modifier = $Modifier; }
		function getModifier() { return $this->Modifier; }
		
		function setCsb_ApplicationId($Csb_ApplicationId) { $this->Csb_ApplicationId = $Csb_ApplicationId; }
		function getCsb_ApplicationId() { return $this->Csb_ApplicationId; }
		
		function setCsb_Application_Status($Csb_Application_Status) { $this->Csb_Application_Status = $Csb_Application_Status; }
		function getCsb_Application_Status() { return $this->Csb_Application_Status; }
		
		function setCsb_Application_Remarks($Csb_Application_Remarks) { $this->Csb_Application_Remarks = $Csb_Application_Remarks; }
		function getCsb_Application_Remarks() { return $this->Csb_Application_Remarks; }
		

		public function __construct() {
			$db = new DbConnect();
			$this->dbConn = $db->connect();
		}
				
		public function insertLV() {
			

			$sql = 'INSERT INTO csb_application_status (Creator,Created,Csb_ApplicationId,Csb_Application_Status,Csb_Application_Remarks) 
												VALUES(:Creator,:Created,:Csb_ApplicationId,:Csb_Application_Status,:Csb_Application_Remarks)';
			
			$stmt = $this->dbConn->prepare($sql);
			$stmt->bindParam(':Creator', $this->Creator);
			$stmt->bindParam(':Created', $this->Created);
			$stmt->bindParam(':Csb_ApplicationId', $this->Csb_ApplicationId);
			$stmt->bindParam(':Csb_Application_Status', $this->Csb_Application_Status);
			$stmt->bindParam(':Csb_Application_Remarks', $this->Csb_Application_Remarks);
			
			if($stmt->execute()) {				 
			
				 // $SysId = $this->dbConn->lastInsertId();
				 // return $SysId;
				 
				return true;
				
			} else {
				return false;
			}
		}
	}
 ?>