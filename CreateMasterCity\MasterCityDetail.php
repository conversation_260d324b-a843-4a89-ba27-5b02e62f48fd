<?php 
	include_once '../Lib/jwt.php';
	include_once 'restMasterCityDetail.php';
	
	class MasterCityDetail extends RestMasterCity {
		
		public function __construct() {
			parent::__construct();
		}

		public function addMasterCity() {
			
			
			$data = json_decode($this->request, true);
			
			foreach($data as $obj){
				
			$MappingId = $this->validateParameter('MappingId', $obj['MappingId'], STRING, false);
			$Title = $this->validateParameter('Title', $obj['Title'], STRING, false);
			$ParentId = $this->validateParameter('ParentId', $obj['ParentId'], STRING, false);
			//$IncomeClass = $this->validateParameter('IncomeClass', $obj['IncomeClass'], STRING, false);
			$OrderNumber = $this->validateParameter('OrderNumber', $obj['OrderNumber'], INTEGER, false);
			
			
			$mrm = new MasterCityModel;
			$mrm->setMappingId($MappingId);
			$mrm->setTitle($Title);
			$mrm->setParentId($ParentId);
			//$mrm->setIncomeClass($IncomeClass);
			$mrm->setOrderNumber($OrderNumber);
			$mrm->setCreated(date('Y-m-d'));
			$mrm->setCreator($this->userId);
			
			
			if(!$mrm->insertCM()) {
				
				 $message = 'Failed to insert.';
			} else {
							
				$message = 'Inserted successful.';
				  
				// $message = $mrm->insertLV();
			}
			
		  }
			  $this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function updateMasterCity() {
			
			
			$data = json_decode($this->request, true);
			
			foreach($data as $obj){
				
			$MappingId = $this->validateParameter('MappingId', $obj['MappingId'], STRING, false);
			$Title = $this->validateParameter('Title', $obj['Title'], STRING, false);
			$ParentId = $this->validateParameter('ParentId', $obj['ParentId'], STRING, false);
			$IncomeClass = $this->validateParameter('IncomeClass', $obj['IncomeClass'], STRING, false);
			$OrderNumber = $this->validateParameter('OrderNumber', $obj['OrderNumber'], INTEGER, false);
			
			
			$mrm = new MasterCityModel;
			$mrm->setSysId($_GET['SysId']);
			$mrm->setMappingId($MappingId);
			$mrm->setTitle($Title);
			$mrm->setParentId($ParentId);
			$mrm->setIncomeClass($IncomeClass);
			$mrm->setOrderNumber($OrderNumber);
			$mrm->setLastModified(date('Y-m-d'));
			$mrm->setModifier($this->userId);
			
			
			if(!$mrm->updateCM()) {
				$message = 'Failed to update.';
			} else {
				$message = "Update successfully.";
			}
			
		  }
			$this->returnResponse(SUCCESS_RESPONSE, $message);
		}
		
		public function getMasterCityDetails() {
			
			if(!isset($_GET['Title']) || $_GET['Title'] == "") {
				
				$mastercity = new MasterCityModel;
				$mastercity = $mastercity->getAllMasterCity();
				$this->returnResponse(SUCCESS_RESPONSE, $mastercity);
				//$this->returnResponseGetAll($mastercity);				
				
			}else{
			
				$Title = $this->validateParameter('Title', $_GET['Title'], STRING);
			
				$mastercity = new MasterCityModel;
				$mastercity->setUserID($Title);
				
				$mastercity = $mastercity->getMasterCityByTitle();
				if(!is_array($mastercity)) {
					$this->returnResponse(SUCCESS_RESPONSE, ['message' => 'user details not found.']);
				}
											
				$this->returnResponse(SUCCESS_RESPONSE, $mastercity);
			}
			
		}
	}
	
 ?>