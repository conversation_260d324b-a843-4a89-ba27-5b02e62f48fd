<?php


class Acumatica {

	function __construct(){
			
		}

	function login($company){
		$server = 'https://ciclo.acumatica.com/entity/auth/login';

		$companyName = companyChecker($company);
		
		$arr = array();
		$arr = ['name' => 'Admin','password' =>'P@ssw0rd','company' => $companyName];
		$json = json_encode($arr);
		
		$cookie_jar = tempnam('/tmp','cookie');
			
		// Initiate Connection
		$curl = curl_init();

		// Login to Acumatica REST API
		curl_setopt_array($curl, array(
			CURLOPT_URL => $server,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 10000,
			CURLOPT_COOKIESESSION => 1,
			CURLOPT_COOKIEJAR => $cookie_jar,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => "POST",
			CURLOPT_POSTFIELDS => $json,
			CURLOPT_HTTPHEADER => array("cache-control: no-cache", "content-type: application/json"
				),
		));
		
		$response = curl_exec($curl);
		$err = curl_error($curl);
		
		if ($err) {
			return "cURL Error #:" . $err;
		} else {
			return $response;	
		}	
	}

	function companyChecker($company){
		$value = '';
		switch($company){
			case 'EUT':
				$value = 'END USER TRAINING';
				break;
			case 'CSC':
				$value = 'CICLO SUERTE CORPORATION CORPORATION';
				break;	
			case 'CMC':
				$value = 'CELEBES MERCHANTILE CORPORATION';
				break;			
		}
		return $value;
	}
	
	function sendToAcumatica($server,$data,$method,$company,$txn,$branch){
				
		$cookie_jar = tempnam('/tmp','cookie');	
		$server1 = 'https://ciclo.acumatica.com/entity/auth/login';
		
		$compCode = $this->companyChecker($company);
			
		$arr = array();
		
		if($txn=='PO'){
		
			switch($company){
				case 'EUT':
					$branch = 'CHO000';
					break;
				case 'CSC':
					$branch = 'CHO000';
					break;
				case 'CMC':
					$branch = 'MHO000';
					break;		
			}
		
			$arr = ['name' => 'Admin','password' =>'P@ssw0rd','company' => $compCode,'branch' => $branch];
		}else {
			$arr = ['name' => 'Admin','password' =>'P@ssw0rd','company' => $compCode,'branch' => $branch];
		}
		
		$json = json_encode($arr);

		$curl = curl_init();
		curl_setopt_array($curl, array(
		  CURLOPT_URL => $server1,
		  CURLOPT_RETURNTRANSFER => true,
		  CURLOPT_ENCODING => "",
		  CURLOPT_COOKIESESSION => 1,
		  CURLOPT_COOKIEJAR => $cookie_jar,
		  CURLOPT_COOKIESESSION => 1,
		  CURLOPT_COOKIEJAR => $cookie_jar,
		  CURLOPT_SSL_VERIFYPEER => false,
		  CURLOPT_SSL_VERIFYHOST => false,
		  CURLOPT_MAXREDIRS => 10,
		  CURLOPT_TIMEOUT => 10000,
		  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		  CURLOPT_CUSTOMREQUEST => "POST",
		  CURLOPT_POSTFIELDS => $json,
		  CURLOPT_HTTPHEADER => array(
			"Cache-Control: no-cache",
			"Content-Type: application/json"
		  )
		));
		curl_exec($curl);
			
		curl_setopt_array($curl, array(
			CURLOPT_URL => $server,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 10000,
			CURLOPT_COOKIESESSION => 1,
			CURLOPT_COOKIEFILE => $cookie_jar,
			CURLOPT_SSL_VERIFYPEER => false,
			CURLOPT_SSL_VERIFYHOST => false,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => $method,		
			CURLOPT_POSTFIELDS => $data,
			CURLOPT_HTTPHEADER => array(
			"Cache-Control: no-cache",
			"Content-Type: application/json"
		  )
		));
		
		$response = curl_exec($curl);
		$err = curl_error($curl);
		curl_close($curl);
		if ($err) {
			return "cURL Error #:" . $err;
		} else {
			return $response;
		}
		//unlink($cookie_jar);
	}
	
	function getFromAcumatica($server,$company)
	{
		$companyName = $this->companyChecker($company);
		
		$arr = array();
		$arr = ['name' => 'Admin','password' =>'P@ssw0rd','company' => $companyName];
		$json = json_encode($arr);
		$cookie_jar = tempnam('/tmp','cookie');
		$curl = curl_init();		
		
		curl_setopt_array($curl, array(
		  CURLOPT_URL => 'https://ciclo.acumatica.com/entity/auth/login',
		  CURLOPT_RETURNTRANSFER => true,
		  CURLOPT_ENCODING => "",
		  CURLOPT_COOKIESESSION => 1,
		  CURLOPT_COOKIEJAR => $cookie_jar,
		  CURLOPT_SSL_VERIFYPEER => false,
		  CURLOPT_SSL_VERIFYHOST => false,
		  CURLOPT_MAXREDIRS => 10,
		  CURLOPT_TIMEOUT => 10000,
		  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		  CURLOPT_CUSTOMREQUEST => "POST",
		  CURLOPT_POSTFIELDS => $json,
		  CURLOPT_HTTPHEADER => array(
			"Cache-Control: no-cache",
			"Content-Type: application/json"
		  )
		));
		curl_exec($curl);
		
		curl_setopt_array($curl, array(
			CURLOPT_URL => $server,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 10000,
			CURLOPT_COOKIESESSION => 1,
			CURLOPT_COOKIEFILE => $cookie_jar,
			CURLOPT_SSL_VERIFYPEER => false,
			CURLOPT_SSL_VERIFYHOST => false,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => "GET",			
			CURLOPT_HTTPHEADER => array(
			"Cache-Control: no-cache",
			"Content-Type: application/json"
		  )
		));
		
		$response = curl_exec($curl);
		$err = curl_error($curl);
		curl_close($curl);
		if ($err) {
			return "cURL Error #:" . $err;
		} else {
			return $response;
		}		
			
	}
	
	
	function getItemForLead(){ //Basic Info, Product and Address insert to lead
	//function getItemForLead($LosNbr){
		$LosNbr = 1;
		include('db_connect.php');
		$res = array();
		$Leadarray= array();
		
		$server = 'https://ciclo.acumatica.com/Entity/Default/18.200.001/Lead';
		$server2 = 'https://ciclo.acumatica.com/Entity/CRM/18.200.001/LEAD';
		
		$str = "select case when Gender = 'MALE' then 'Mr' else 'Ms' end Title,
					a.FirstName FName,
					a.LastName LName,
					Null CompanyName,
					Null JobTitle,
					'LOS' LeadClass,
					a.PhoneNumber PhoneNumber,
					Null ParenAccount,
					b.Brand,
					b.Model,
					b.ColorOfUnit,
					b.FinancingBranch,
					Null CompanyCode,

					concat(c.BlkNumber,' ',Street) Street,
					c.Barangay,
					c.City,
					c.Province,
					Null PostalCode

					from client_indv_personal_info a

					left join Mc_loan_application b
					on a.SysId = b.ClientInfoId

					left join client_indv_address_current c
					on a.SysId = c.ClientId";
					
		// $str = "SELECT 'MR.', 'JUAN', 'DELA CRUZ SR.', 'JUAN DELA CRUZ SR.', 'OFFICE STAFF', 'LOS', 
						// '************', 'C0000000011', 'HONDA', 'CLICK 150', 'BLACK', 'MAB003', 'EUT',
						// '#123 Bagong Bayan', 'Lapu-Lapu', 'Silang', 'Cavite', '1112'"; //Basic Info, Product and Address
						
	//$sql = sqlsrv_query($conn,$str,array($LosNbr));
		
		$sql = sqlsrv_query($conn,$str,array());
		if($sql){
			if(sqlsrv_has_rows($sql)){
				$row = sqlsrv_fetch_array($sql);
				
				//Basic information
				$row1 = [value => trim($row[0])]; //Title
				$row2 = [value => trim($row[1])]; //FName
				$row3 = [value => trim($row[2])]; //LName
				$row4 = [value => trim($row[3])]; //CompanyName
				$row5 = [value => trim($row[4])]; //JobTitle
				$row6 = [value => trim($row[5])]; //LeadClass
				$row7 = [value => trim($row[6])]; //PhoneNumber
				$row8 = [value => trim($row[7])]; //ParenAccount or Financing Bank
				
				$row9 = [value => trim($row[8])]; //Attribute - Brand
				$row10 = [value => trim($row[9])]; //Attribute - Model Description
				$row11 = [value => trim($row[10])]; //Attribute - Color Description
				//Basic information
				
				$branchCode = trim($row[11]);
				$companyCode = trim($row[12]);
				
				//Address
				$row12 = [value => trim($row[13])]; //House#/Block#/Village/Street
				$row13 = [value => trim($row[14])]; //Barangay
				$row14 = [value => trim($row[15])]; //City
				$row15 = [value => trim($row[16])]; //Province
				$row16 = [value => trim($row[17])]; //Postal Code
				//Address
				
						$Attrow1 = [value => 'BRAND'];
						$Valrow1 = $row9;
						$Attrow2 = [value => 'Preferred Unit Model'];
						$Valrow2 = $row10;
						$Attrow3 = [value => 'Preferred Color'];
						$Valrow3 = $row11;
						
						$Attrib1 = [Attribute => $Attrow1,
								Value => $Valrow1
						];
						$Attrib2 = [Attribute => $Attrow2,
								Value => $Valrow2
						];
						$Attrib3 = [Attribute => $Attrow3,
								Value => $Valrow3
						];
						
				
				array_push($Leadarray,$Attrib1, $Attrib2, $Attrib3);
				
				$res =['Title' => $row1,
					 'FirstName' => $row2,
					 'LastName' => $row3,
					 'CompanyName' => $row4,
					 'JobTitle' => $row5,
					 'LeadClass' => $row6,
					 'Phone2' => $row7,
					 'ParentAccount' => $row8,
					 'Attributes' => $Leadarray
				];
				
				$data = json_encode($res);	
				$sendLEad = $this->sendToAcumatica($server,$data,'PUT',$companyCode,null,$branchCode);
				
				//$leadres = sqlsrv_query($conn,"INSERT INTO GenericData_LOS VALUES (?,?,?,GETDATE())",array($OasNbr,'LD',$sendLEad));						

				$leadres= sqlsrv_query($conn,"INSERT INTO GenericData_LOS VALUES (?,?,?,GETDATE())",array($LosNbr,'LEAD',$sendLEad));	
				if($leadres){
					$qLead = "SELECT JSON_VALUE(jsonData,'$.LeadID.value') LeadID
								FROM GenericData_LOS GenericData , 
									(SELECT Max(RecId)RecId,RequestId FROM 
									GenericData_LOS WHERE RequestId = ? AND Type = 'LEAD' GROUP BY RequestId) J
								WHERE  GenericData.RequestId = ? AND Type = 'LEAD' AND J.RecId = GenericData.RecId";	
										
					$SqlLead = sqlsrv_fetch_array(sqlsrv_query($conn,$qLead,array($LosNbr,$LosNbr)));
					if(!empty($SqlLead[0])){
						
						//$strLead='UPDATE "MCLOANTABLE" SET AcuLeadID=? where LOSNumber = ?'; --------->>Update MCLoan Table
						//sqlsrv_query($conn,$strLead,array($LeadID[0],$LosNbr));
						
						$LeadID = [value => trim($SqlLead[0])];	
							
							$res =['LeadID' => $LeadID,
									 'Street' => $row12,
									 'Brangay' => $row13,
									 'City' => $row14,
									 'Province' => $row15,
									 'PostalCode' => $row16
								];
							$data = json_encode($res);	
							$updateLEad = $this->sendToAcumatica($server2,$data,'PUT',$companyCode,null,$branchCode);
						
					//GET OPPORTUNITYID
						$param = urlencode(" eq ".$SqlLead[0]."");
						$server3 = 'https://ciclo.acumatica.com/Entity/Default/18.200.001/Opportunity?$filter=ContactID'.$param;
						$getLD = $this->getFromAcumatica($server3,$companyCode);

						$LDres= '{ "jsonValue" :'.$getLD.'}';

						$LD = sqlsrv_query($conn,"INSERT INTO GenericData_LOS VALUES (?,?,?,GETDATE())",array($LosNbr,'GETLD',$LDres));
			
						if($LD){
							$strLD = "DECLARE @json nvarchar(max) = (SELECT jsonData	FROM [GenericData_LOS]  GenericData, 
										(SELECT Max(RecId)RecId,RequestId FROM GenericData_LOS WHERE RequestId = ?
										AND Type = 'GETLD' GROUP BY RequestId) J
										WHERE  GenericData.RequestId = ? AND Type = 'GETLD'
										AND J.RecId = GenericData.RecId);
									SELECT [OpportunityID]
								FROM OPENJSON(@json,'$.jsonValue')
								WITH ([OpportunityID] [nvarchar](35) '$.OpportunityID.value') As json";	
														
							$OppID = sqlsrv_fetch_array(sqlsrv_query($conn,$strLD,array($LosNbr,$LosNbr)));
							if(!empty($OppID[0])){
								//$strOpp='UPDATE "MCLOANTABLE" SET AcuOpportunityID=? where LOSNumber = ?'; --------->>Update MCLoan Table
								//sqlsrv_query($conn,$strOpp,array($OppID[0],$LosNbr));
							}
						}
					}
				}
			} else
			{
				$res = ['message' => 'NO RECORD FOUND','statusCode'=>'-1005'];		
			}
		}else {
			$res = ['message' => 'UNKNOWN ERROR'. print_r(sqlsrv_errors(),true),'statusCode'=>'-1000'];
		}
		
		sqlsrv_close($conn);
		//return json_encode($res);
		return $OppID[0];	
	}
	
	function OpportunityCIS(){ //CUSTOMER INFORMATION SHEET
	//function OpportunityCIS($LosNbr,$companyCode, $branchCode, $OpportunityID){
		$LosNbr = 1; //SAMPLE
		$companyCode='EUT';  //SAMPLE EUT
		$branchCode='MAB003'; //SAMPLE BRANCH
		$OpportunityID='**********'; //Opportunity ID
		$Brand='HONDA'; //SAMPLE BRAND
		$Model='CLICK 150'; //SAMPLE MODEL Description
		
		include('db_connect.php');
		$res = array();
		
		$server = 'https://ciclo.acumatica.com/Entity/CRM/18.200.001/OPPORTUNITY';
	/* 
		$str = "SELECT 'Individual', 'No', 'Service', '100000', '15000', '36','Walk-In', 
						'Yes', 'Male', 'SR.', 'Unmarried', 'College Degree', 'DALISAY',
						'JUAN DELA CRUZ IV', '10/25/1992', 'ROXAS, ISABELA', '<EMAIL>', '09065645345',
						'(02)1723-82','Owned','Townhouse','Family Compound','Concrete','Filipino','Filipino','Present',
						'***********-000' TIN,'04-4092994-5' SSSID,'' GSIS,'' DRIVERLICENSE, 'Private Employee',
						'#123 dyan lang St., Mayapa Village, Brgy. Samay','Pasig City','Metro Manila','Philippines',
						'#123 dyan */
		$str = "SELECT LoanType, CASE IsRepeatBorrower WHEN 1 THEN 'YES' WHEN 0 THEN 'NO' END IsRepeatBorrower,A.Purpose, SellingPrice,
					A.DownPayment, A.Term, NULL LeadSource, NULL IsPhilResident, B.Gender, B.SuffixName, B.CivilStatus, B.EducationalAttainment,
					NULL MothersMaiden, NULL FathersName, B.DOB, B.BirthPlace, B.Email, B.MobileNumber, B.PhoneNumber, ISNULL(D.HomeOwnership, C.Type) HomeOwnership,
					D.HomeType HouseType, c.CurrAddress HouseLocation, NULL HouseisMadeof, b.Nationality, NULL MailingAddress,
					CASE WHEN E.Type='TIN' THEN E.IdNumber END TIN, CASE WHEN E.Type='SSS ID' THEN E.IdNumber END SSSId, CASE WHEN E.Type='GSIS ID' THEN E.IdNumber END GSISId,
					CASE WHEN E.Type='DRIVER''S LICENSE' THEN E.IdNumber END DriverLicense, ISNULL(G.EmploymentStatus,H.EmploymentStatus) EmploymentStatus,
					CONCAT(C.CurrStreet,' ',c.CurrBrgy) CurrStreetBrgy, c.CurrCity, c.CurrProv, 'Philippines' CurrCountry,
					CONCAT(CC.PermanentStreet,' ',CC.PermanentBrgy) PermanentStreetBrgy, CC.PermanentCity, CC.PermanentProv, 'Philippines' PermanentCountry, F.Category, A.ClientInfoId
				FROM Mc_loan_application A
				INNER JOIN client_indv_personal_info B 
					ON A.ClientInfoId=B.SysId
				LEFT JOIN 
					(
						SELECT A.ClientId, A.Type, CONCAT(A.BlkNumber,' ',A.Street,' ',D.Title,', ',C.Title,', ',B.Title) CurrAddress,
							A.Street CurrStreet, D.Title CurrBrgy, C.Title CurrCity, B.Title CurrProv		
						FROM client_indv_address_current A
						LEFT JOIN master_province B 
							ON A.Province=B.ParentId
						LEFT JOIN master_city C
							ON A.City=C.ParentId
						LEFT JOIN master_barangay D
							ON A.Barangay=D.SysId
					) C
					ON B.SysId=C.ClientId
				LEFT JOIN 
					(
						SELECT A.ClientId, A.Type, CONCAT(A.BlkNumber,' ',A.Street,' ',D.Title,', ',C.Title,', ',B.Title) PermanentAddress,
							A.Street PermanentStreet, D.Title PermanentBrgy, C.Title PermanentCity, B.Title PermanentProv		
						FROM client_indv_address_permanent A
						LEFT JOIN master_province B 
							ON A.Province=B.ParentId
						LEFT JOIN master_city C
							ON A.City=C.ParentId
						LEFT JOIN master_barangay D
							ON A.Barangay=D.SysId
					) CC
					ON B.SysId=CC.ClientId
				LEFT JOIN
					(
						SELECT A.ApplicationID, CASE WHEN B.Question='What is the applicant''s home ownership?' then B.Answer end HomeOwnership,
							CASE WHEN B.Question='What is the applicant''s home type?' then B.Answer end HomeType
						
						FROM ftv_job A
						LEFT JOIN ftv_job_result_q_n_aa B
							ON A.SysId = B.JobID
							AND B.Verified = 1	
					) D ON A.SysId=D.ApplicationID
				LEFT JOIN client_indv_identification E
					ON B.SysId=E.ClientId
				LEFT JOIN client_indv_source_of_income F
					ON B.SysId=F.ClientId
				LEFT JOIN client_indv_soi_goverment_employee G
					ON F.SysId=G.ParentId
				LEFT JOIN client_indv_soi_private_employee H
					ON F.SysId=H.ParentId";
		$sql = sqlsrv_query($conn,$str,array());
		if($sql){
			if(sqlsrv_has_rows($sql)){
				$row = sqlsrv_fetch_array($sql);
				
				$OppID= [value => trim($OpportunityID)];
				$Brand= [value => trim($Brand)];
				$Model= [value => trim($Model)];
				
				//Basic information
				$row1 = [value => trim($row[0])]; //LoanType	
				$row2 = [value => trim($row[1])]; //RepeatBorrower
				$row3 = [value => trim($row[2])]; //Purpose
				$row4 = [value => trim($row[3])]; //SellingPrice
				$row5 = [value => trim($row[4])]; //DownPayment
				$row6 = [value => trim($row[5])]; //Term
				$row7 = [value => trim($row[6])]; //LeadSource
				
				$row8 = [value => trim($row[7])]; //PhilResident
				$row9 = [value => trim($row[8])]; //Gender
				$row10 = [value => trim($row[9])]; //Suffix
				$row11 = [value => trim($row[10])]; //MaritalStatus
				$row12 = [value => trim($row[11])]; //EducAttainment
				$row13 = [value => trim($row[12])]; //MothersMaidenName
				$row14 = [value => trim($row[13])]; //FathersName
				$row15 = [value => trim($row[14])]; //BirthDay
				$row16 = [value => trim($row[15])]; //BirthPlace
				$row17 = [value => trim($row[16])]; //EmailAddress
				$row18 = [value => trim($row[17])]; //MobilePhone
				$row19 = [value => trim($row[18])]; //TelephoneNo
				
				$row20 = [value => trim($row[19])]; //HomeOwnership
				$row21 = [value => trim($row[20])]; //HouseType
				$row22 = [value => trim($row[21])]; //HouseLocation
				$row23 = [value => trim($row[22])]; //Houseismadeof
				
				$row24 = [value => trim($row[23])]; //Nationality
				$row25 = [value => trim($row[24])]; //Citizenship
				$row26 = [value => trim($row[25])]; //MailingAddress
			
			//VALID ID	
				$row27 = [value => trim($row[26])]; //TIN
				$row28 = [value => trim($row[27])]; //SSSNo
				$row29 = [value => trim($row[28])]; //GSISNo
				$row30 = [value => trim($row[29])]; //DriverLicenseNo
				
				$row31 = [value => trim($row[30])]; //EmploymentStatus
				
			//Present Address
				$row32 = [value => trim($row[31])]; //Present_Street
				$row33 = [value => trim($row[32])]; //Present_City
				$row34 = [value => trim($row[33])]; //Permanent_Province
				$row35 = [value => trim($row[34])]; //Present_Country
			//Permanent Address
				$row36 = [value => trim($row[35])]; //Permanent_StreetBgy
				$row37 = [value => trim($row[36])]; //Permanent_City
				$row38 = [value => trim($row[37])]; //Permanent_Province
				$row39 = [value => trim($row[38])]; //Permanent_Country
				$row40 = [value => trim($row[39])]; //soi_category
				$row41 = [value => trim($row[40])]; //client_id
				
				
				$res =['OpportunityID' => $OppID,
					 'LoanType' => $row1,					//Individual / Institutional
					 'RepeatBorrower' => $row2,				//Yes / No
					 'Purpose' => $row3,					//Service / Business / TODA / Others
					 'SellingPrice' => $row4,
					 'DownPayment' => $row5,
					 'Term' => $row6,						//6 / 12 / 18 / 24 / 30 / 36
					 'LeadSource' => $row7,					//TODA / Agent / Walk-In / Institutional / IC/ES / Cross Selling
					 'Brand' => $Brand, //SAMPLE ONLY		//Yamaha / Kawasaki / Suzuki / Honda / Kymco / TVS
					 'UnitModel' => $Model, //SAMPLE ONLY
				
				//OTHER INFO
					 'PhilResident' => $row8,
					 'Gender' => $row9,						//Male / Female
					 'Suffix' => $row10,					//JR. / SR. / II / III / IV / CPA / MD / PHD / ENGR. / RN 
					 'MaritalStatus' => $row11, 			//Unmarried / Separated / Married / Divorced/Annulled / Widowed
					 'EducAttainment' => $row12,			//Doctorate / Master's Degree / Associate/Vocational Graduate / Senio High School Graduate / High School Graduate / Elementary Graduate / No Formal Schooling
					 'MothersMaidenName' => $row13,
					 'FathersName' => $row14,
					 'BirthDay' => $row15,
					 'BirthPlace' => $row16,
					 'EmailAddress' => $row17,
					 'MobilePhone' => $row18,
					 'TelephoneNo' => $row19,
				
				//HOME OWNERSHIP
					 'HomeOwnership' => $row20,						//Owned / Used Free / Rented / Mortgaged / Living with Relatives
					 'HouseType' => $row21,							//Bungalow / Townhouse / 2 Storey / Condominium / Duplex / Single Detached / Room Dweller / Others
					 'HouseLocation' => $row22,						//Exclusive Subdivision / Commercial / Critical Area / Squatter Area / Non-Exclusive Subdivision / Relocation / Drug Prone / Banned Area / Residential . Family Compound / Flood Prone / Others
					 'Houseismadeof' => $row23,						//Concrete / Hald Concrete/Half Wood / Wood/Light Material / Others
					 'Nationality' => $row24,
					 'Citizenship' => $row25,
					 'MailingAddress' => $row26,					//Present / Permanent / Business
					 
				//VALID ID
					 'TIN' => $row27,
					 'SSSNo' => $row28,
					 'GSISNo' => $row29,
					 'DriverLicenseNo' => $row30,
					 
					 'EmploymentStatus' => $row31,					//Private Employee / Govt. Employee / Self-Employed / Business / Remittance / Pension/Retired / Farmer / OFW / Others
					 
				//Present Address	 
					 'Present_Street' => $row32,
					 'Present_City' => $row33,
					 'Present_Province' => $row34,
					 'Present_Country' => $row35,
				//Permanent Address		 
					 'Permanent_StreetBgy' => $row36,
					 'Permanent_City' => $row37,
					 'Permanent_Province' => $row38,
					 'Permanent_Country' => $row39,
					 
				if($row[49] == 'PRIVATE EMPLOYEE' || $row[49] == 'GOVERNMENT EMPLOYEE'){
					
					//***IF PRIVATE/GOVERNMENT/OFW EMPLOYEE
					$soi = sqlsrv_query($conn,"SELECT 
								CASE WHEN B.CATEGORY = 'GOVERNMENT EMPLOYEE' THEN c.dateHired
									WHEN B.Category='PRIVATE EMPLOYEE' THEN d.dateHired END datehired, 
								'' Employer, 
								CASE WHEN B.CATEGORY = 'GOVERNMENT EMPLOYEE' THEN C.EmploymentStatus
									WHEN B.Category='PRIVATE EMPLOYEE' THEN D.EmploymentStatus END Status,
								CASE WHEN B.CATEGORY = 'GOVERNMENT EMPLOYEE' THEN CONCAT(c.Street,' ',c.Barangay,', ',c.City,', ',c.Province)
									WHEN B.Category='PRIVATE EMPLOYEE' THEN CONCAT(c.Street,' ',c.Barangay,', ',c.City,', ',c.Province) END Address,
								CASE WHEN B.CATEGORY = 'GOVERNMENT EMPLOYEE' THEN C.NatureOfWork
									WHEN B.Category='PRIVATE EMPLOYEE' THEN D.NatureOfWork END NatureOfWork,
								CASE WHEN B.CATEGORY = 'GOVERNMENT EMPLOYEE' 
										THEN CASE WHEN C.NetIncome < '10000' THEN 'Under 10,000' 
												WHEN C.NetIncome <= '49999' THEN '10,000 - 49,999'
												WHEN C.NetIncome <= '249999' THEN '50,000 - 249,999'
												WHEN C.NetIncome <= '499999' THEN '250,000 - 499,999'
												WHEN C.NetIncome <= '999999' THEN '500,000 - 999,999'
												WHEN C.NetIncome >= '1000000' THEN '1,000,000+'
									WHEN B.Category='PRIVATE EMPLOYEE' 
										THEN CASE WHEN D.NetIncome < '10000' THEN 'Under 10,000' 
												WHEN D.NetIncome <= '49999' THEN '10,000 - 49,999'
												WHEN D.NetIncome <= '249999' THEN '50,000 - 249,999'
												WHEN D.NetIncome <= '499999' THEN '250,000 - 499,999'
												WHEN D.NetIncome <= '999999' THEN '500,000 - 999,999'
												WHEN D.NetIncome >= '1000000' THEN '1,000,000+' END END END Income					
								
							FROM Mc_loan_application A
							INNER JOIN client_indv_source_of_income B
								ON A.ClientInfoId=B.ClientId
							LEFT JOIN client_indv_soi_goverment_employee C
								ON B.SysId=C.ParentId
							LEFT JOIN client_indv_soi_private_employee D
								ON B.SysId=D.ParentId
							WHERE A.ClientInfoId = $row41");
					$soiarr= sqlsrv_fetch_array($soi);
					if($soiarr){
						
						'DateHiredStartofBusiness' => $soiarr[0],
						'Employer' => $soiarr[1],
						'Status' => $soiarr[2],								//Regular / Provisionary / Outsource / Contractual
						'EmploymentAddress' => $soiarr[3],
						'NatureOfWork' => $soiarr[4],
						'GrossMonthlyIncome' => $soiarr[5],   //Under 10,000 / 10,000 - 49,999 / 50,000 - 249,999 / 250,000 - 499,999 / 500,000 - 999,999 / 1,000,000+
					 
					}
					
					 
				}
			/*
				
				//***IF SELF-EMPLOYED / BUSINESS
					 'DateHiredStartofBusiness' => ,
					 'BusinessName' => ,
					 'BusinessPhone' => ,
					 'BusinessAddress' => ,
					 'NatureofSelfBusiness' => ,
					 'GrossMonthlyIncome' => ,  //Under 10,000 / 10,000 - 49,999 / 50,000 - 249,999 / 250,000 - 499,999 / 500,000 - 999,999 / 1,000,000+
					 
			//character references
				//HOME
					 'RefHome_Name' => ,
					 'RefHome_Address' => ,
					 'RefHome_ContactNo' => ,
					 'RefHome_Relation' => ,
				//BARANGAY
					 'RefBrg_Name' => ,
					 'RefBrg_Address' => ,
					 'RefBrg_ContactNo' => ,
					 'RefBrg_Relationship' => ,
				//WORK
					 'RefWork_Name' => ,
					 'RefWork_Address' => ,
					 'RefWork_ContactNo' => ,
					 'RefWork_Relationship' => ,
			
			//Family Expenditure
					 'Food' => , amount only no range
					 'HouseRent' => , amount only no range
					 'Electricity' => , amount only no range
					 'Water' => , amount only no range
					 'Transportation' => , amount only no range
					 'Education' => , amount only no range
					 'ExpOthers' => , sum of not in the list
			
			//SOURCE OF FUNDS		 
					 'SourceBusiness' => , amount only no range
					 'SourceSalary' => , amount only no range
					 'SourceRegRemittances' => ,amount only no range
					 'SourcePension' => , amount only no range
					 'SourceDepositInvestments' => , amount only no range
					 'SourcePasada' => , amount only no range
					 'SourceOthers' => , amount only no range
					 
				//If Married	 
					 'SpouseBusiness' => ,
					 'SpouseSalary' => ,
					 'SpouseRegRemittances' => ,
					 'SpousePension' => ,
					 'SpouseDepositInvestments' => ,
					 'SpousePasada' => ,
					 'SpouseOthers' => ,
				
					 'NoofChildren' => 5,
					 
				//CHILDREN FIELDS ARE BASED ON No.Of Children	 
					 'FirstNameUsrC1FName' => ,
					 'MiddleNameUsrC1MName' => ,
					 'LastNameUsrC1LName' => ,
					 'SchoolOccupationUsrC1School' => ,
					 'GenderUsrC1Gender' => ,
					 'BirthDateUsrC1BDate' => ,
					 
					 'FirstNameUsrC2FName' => ,
					 'MiddleNameUsrC2MName' => ,
					 'LastNameUsrC2LName' => ,
					 'SchoolOccupationUsrC2School' => ,
					 'GenderUsrC2Gender' => ,
					 'BirthDateUsrC2BDate' => ,
					
					 'FirstNameUsrC3FName' => ,
					 'MiddleNameUsrC3MName' => ,
					 'LastNameUsrC3LName' => ,
					 'SchoolOccupationUsrC3School' => ,
					 'GenderUsrC3Gender' => ,
					 'BirthDateUsrC3BDate' => ,
					 
					 'FirstNameUsrC4FName' => ,
					 'MiddleNameUsrC4MName' => ,
					 'LastNameUsrC4LName' => ,
					 'SchoolOccupationUsrC4School' => ,
					 'GenderUsrC4Gender' => ',
					 'BirthDateUsrC4BDate' => ,
					 
					 'FirstNameUsrC5FName' => ,
					 'MiddleNameUsrC5MName' => ,
					 'LastNameUsrC5LName' => ,
					 'SchoolOccupationUsrC5School' => ,
					 'GenderUsrC5Gender' => ,
					 'BirthDateUsrC5BDate' => ,
				//end CHILDREN	 
				*/
				];
				
				$data = json_encode($res);	
				$sendOpp = $this->sendToAcumatica($server,$data,'PUT',$companyCode,null,$branchCode);
			}
		}			
						
		sqlsrv_close($conn);
		//return json_encode($res);
		return $sendOpp;	
	}
	
	function OpportunityProduct(){ //ADD PRODUCT
	//function OpportunityProduct($LosNbr,$companyCode,$branchCode, $OpportunityID){
		$LosNbr = 1; //SAMPLE
		$companyCode='EUT';  //SAMPLE EUT
		$branchCode='MAB003'; //SAMPLE BRANCH
		$OpportunityID='**********'; //Opportunity ID
		
		include('db_connect.php');
		$res = array();
		
		$server = 'https://ciclo.acumatica.com/Entity/CRM/18.200.001/OPPORTUNITY';
		$str = "SELECT 'ACB150CBTL', 'BK', 'WAPA002', 1, 0"; //QUERY PRODUCT OR INVENTORY ITEM
						//AcuInv, COLORID, WAREHOUSE, Quantity, SELLING PRICE 
			$sql = sqlsrv_query($conn,$str,array());
			if($sql){
				if(sqlsrv_has_rows($sql)){
					$row = sqlsrv_fetch_array($sql);
					
					$OppoID = [value => $OpportunityID];
					$row1 = [value => trim($row[0])];
					$row2 = [value => trim($row[1])];
					$row3 = [value => trim($row[2])];
					$row4 = [value => $row[3]];
					$row5 = [value => $row[4]];
					
					$Products = [InventoryID => $row1,
								Subitem => $row2,
								Warehouse => $row3,
								Qty => $row4,
								UnitPrice => $row5
					];
				
					$Opparray= array();
					array_push($Opparray,$Products);
						
					$res =['OpportunityID' => $OppoID,
						 'Products' => $Opparray
					];
					
					$data = json_encode($res);	
					$sendOpp = $this->sendToAcumatica($server,$data,'PUT',$companyCode,null,$branchCode);
				}
			}
				
		sqlsrv_close($conn);
		//return json_encode($res);
		return $sendOpp;	
	}
	
}
?>